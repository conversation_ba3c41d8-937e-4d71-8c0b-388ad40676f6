<!DOCTYPE html>
<html lang="ar" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
    <meta name="description" content="Energy.AI - Smart Energy Solutions with AI-powered optimization. Reduce energy costs by 15-30% with our intelligent energy management systems. Advanced renewable energy solutions for businesses and homes in Jordan and MENA region.">
    <meta name="keywords" content="energy AI, smart energy solutions, renewable energy, energy optimization, artificial intelligence, energy management, cost reduction, sustainability, Jordan energy, MENA energy solutions, solar power, wind energy, smart grid, IoT energy, energy analytics, carbon footprint reduction">
    <meta name="author" content="Energy.AI - Mohammad <PERSON>">
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    <meta name="language" content="en">
    <meta name="theme-color" content="#1976d2">
    <meta name="msapplication-TileColor" content="#1976d2">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Energy.AI">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://energy-ai.netlify.app/">
    <meta property="og:title" content="Energy Ai">
    <meta property="og:description" content="Reduce energy costs by 15-30% with AI-powered energy optimization solutions. Smart energy management for businesses and homes. Advanced renewable energy solutions with real-time monitoring and predictive analytics.">
    <meta property="og:image" content="https://energy-ai.netlify.app/images/icon-512x512.png">
    <meta property="og:image:width" content="512">
    <meta property="og:image:height" content="512">
    <meta property="og:image:alt" content="Energy.AI Logo - Smart Energy Solutions">
    <meta property="og:site_name" content="Energy.AI">
    <meta property="og:locale" content="en_US">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://energy-ai.netlify.app/">
    <meta name="twitter:title" content="Energy Ai">
    <meta name="twitter:description" content="Reduce energy costs by 15-30% with AI-powered energy optimization solutions. Advanced renewable energy management systems.">
    <meta name="twitter:image" content="https://energy-ai.netlify.app/images/icon-512x512.png">
    <meta name="twitter:image:alt" content="Energy.AI - Smart Energy Solutions">
    <meta name="twitter:creator" content="@EnergyAI_Jordan">
    <meta name="twitter:site" content="@EnergyAI_Jordan">

    <!-- Canonical and Alternate Languages -->
    <link rel="canonical" href="https://energy-ai.netlify.app/">
    <link rel="alternate" hreflang="en" href="https://energy-ai.netlify.app/">
    <link rel="alternate" hreflang="ar" href="https://energy-ai.netlify.app/?lang=ar">
    <link rel="alternate" hreflang="x-default" href="https://energy-ai.netlify.app/">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Google Fonts for Arabic Support -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Favicons -->
    <link rel="icon" type="image/png" sizes="32x32" href="/images/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/images/icon-192x192.png">
    <link rel="apple-touch-icon" href="/images/icon-192x192.png">

    <title>Energy Ai</title>

    <!-- Modern Glassmorphism CSS - Inline for performance -->
    <style>
        /* Modern Glassmorphism Critical Styles */
        :root {
            --primary-color: #1976d2;
            --secondary-color: #ff7200;
            --background-primary: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --glass-bg: rgba(255, 255, 255, 0.08);
            --glass-border: rgba(255, 255, 255, 0.15);
            --transition-fast: 0.15s ease;
            --transition-normal: 0.3s ease;
        }

        [data-theme="light"] {
            --background-primary: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 25%, #ddeeff 50%, #d4e9ff 75%, #cce4ff 100%);
            --text-primary: #2c3e50;
            --text-secondary: #5d4e37;
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.3);
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Noto Sans Arabic', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Tahoma, Arial, sans-serif;
            background: var(--background-primary);
            background-attachment: fixed;
            color: var(--text-primary);
            overflow-x: hidden;
            transition: all var(--transition-normal);
            direction: rtl;
            text-align: right;
            line-height: 1.6;
        }

        /* Ensure Arabic text renders properly */
        * {
            font-feature-settings: "liga" 1, "kern" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Fix direction for specific elements that should be LTR */
        input[type="number"], input[type="email"], input[type="url"],
        .ltr, code, pre {
            direction: ltr;
            text-align: left;
        }

        /* Ensure buttons and labels display Arabic text properly */
        button, label, select, option {
            font-family: 'Noto Sans Arabic', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Tahoma, Arial, sans-serif;
        }

        /* Fix select dropdown direction */
        select {
            direction: rtl;
            text-align: right;
        }

        /* Fix placeholder text */
        input::placeholder, textarea::placeholder {
            direction: rtl;
            text-align: right;
        }

        /* Glassmorphism Background */
        .glassmorphism-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: var(--background-primary);
        }

        /* Floating Shapes */
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .shape {
            position: absolute;
            background: linear-gradient(135deg, rgba(255, 114, 0, 0.08), rgba(25, 118, 210, 0.08));
            border-radius: 50%;
            animation: float 25s infinite linear;
        }

        .shape:nth-child(1) { width: 120px; height: 120px; top: 15%; left: 8%; animation-delay: 0s; }
        .shape:nth-child(2) { width: 180px; height: 180px; top: 65%; left: 85%; animation-delay: 8s; }
        .shape:nth-child(3) { width: 90px; height: 90px; top: 85%; left: 15%; animation-delay: 16s; }
        .shape:nth-child(4) { width: 150px; height: 150px; top: 25%; left: 75%; animation-delay: 24s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
            25% { transform: translateY(-40px) rotate(90deg) scale(1.1); }
            50% { transform: translateY(-20px) rotate(180deg) scale(0.9); }
            75% { transform: translateY(-60px) rotate(270deg) scale(1.05); }
        }

        /* Glass Components */
        .glass {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .main {
            min-height: 100vh;
            position: relative;
        }

        .navbar {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            width: 90%;
            max-width: 1200px;
            padding: 15px 30px;
            transition: all var(--transition-normal);
        }

        /* Critical inline styles for immediate loading */
        .loading-screen {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: var(--background-primary) !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            z-index: 9999 !important;
            transition: opacity 0.8s ease !important;
            overflow: hidden !important;
        }

        .loading-screen.hidden {
            opacity: 0 !important;
            pointer-events: none !important;
        }

        /* Energy Cards Styles (Optimization + Security + Cloud) */
        .energy-optimization-card,
        .energy-security-card,
        .cloud-energy-card {
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .energy-optimization-card:hover,
        .energy-security-card:hover,
        .cloud-energy-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(25, 118, 210, 0.3);
        }

        .card-action {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
        }

        .energy-optimization-card:hover .card-action,
        .energy-security-card:hover .card-action,
        .cloud-energy-card:hover .card-action {
            opacity: 1;
            transform: translateY(0);
        }

        .action-text {
            font-size: 14px;
            color: #1976d2;
            font-weight: 500;
        }

        .action-arrow {
            font-size: 18px;
            color: #1976d2;
            transition: transform 0.3s ease;
        }

        .energy-optimization-card:hover .action-arrow,
        .energy-security-card:hover .action-arrow,
        .cloud-energy-card:hover .action-arrow {
            transform: translateX(5px);
        }



        /* Energy Optimization Modal Styles */
        .energy-optimization-modal,
        .energy-security-modal,
        .cloud-energy-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 10000;
            display: none;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .energy-optimization-modal.active,
        .energy-security-modal.active,
        .cloud-energy-modal.active {
            display: flex;
            opacity: 1;
        }



        .modal-container {
            display: block;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 30%, #16213e 50%, #0f3460 70%, #533483 100%);
            position: relative;
            overflow: hidden;
        }

        .modal-sidebar {
            width: 280px;
            background: transparent;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: 24px;
            display: flex;
            flex-direction: column;
            position: absolute;
            top: 0;
            left: 0;
            height: 100vh;
            transform: translateX(-270px);
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
            z-index: 10001;
            opacity: 0;
            box-shadow: 2px 0 20px rgba(0, 0, 0, 0.5);
        }

        .modal-sidebar::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -8px;
            transform: translateY(-50%);
            width: 8px;
            height: 40px;
            background: rgba(25, 118, 210, 0.05);
            border-radius: 0 6px 6px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.4s ease;
            opacity: 0.3;
        }

        .modal-sidebar::before {
            content: '⋮';
            position: absolute;
            top: 50%;
            right: -6px;
            transform: translateY(-50%);
            color: rgba(25, 118, 210, 0.4);
            font-size: 12px;
            z-index: 1001;
            pointer-events: none;
            transition: all 0.4s ease;
            opacity: 0.5;
        }

        .modal-sidebar:hover {
            transform: translateX(0);
            opacity: 1;
        }

        .modal-sidebar:hover::after {
            background: rgba(25, 118, 210, 0.15);
            width: 12px;
            opacity: 0.8;
        }

        .modal-sidebar:hover::before {
            opacity: 0;
            transform: translateY(-50%) scale(0.5);
        }

        .sidebar-hover-area {
            position: absolute;
            top: 0;
            left: 0;
            width: 20px;
            height: 100vh;
            z-index: 10000;
            background: transparent;
        }









        /* تحسين الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .modal-sidebar {
                transform: translateX(-290px);
                opacity: 0;
                width: 90vw;
                max-width: 320px;
            }

            .sidebar-hover-area {
                width: 15px;
            }

            .modal-sidebar::after {
                right: -6px;
                width: 6px;
                height: 30px;
                opacity: 0.2;
            }

            .modal-sidebar::before {
                right: -4px;
                font-size: 10px;
                opacity: 0.3;
            }

            .modal-main-content {
                width: 100vw;
                height: 100vh;
            }

            .modal-chat-area {
                padding: 0;
                height: calc(100vh - 100px);
            }

            .chat-content-area {
                padding: 20px 15px;
                max-width: 95%;
            }

            .messages-container {
                padding: 20px 15px;
                max-width: 95%;
            }

            .message {
                max-width: 95%;
                width: 95%;
            }

            .modal-input-wrapper {
                max-width: 95%;
                width: 95%;
            }
        }

        .modal-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 32px;
            padding-bottom: 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #1976d2, #ff7200);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .modal-logo-text {
            font-size: 18px;
            font-weight: 600;
        }

        .modal-close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #ffffff;
            font-size: 20px;
            transition: all 0.3s ease;
            z-index: 10001;
        }

        .modal-close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .sidebar-close-section {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-close-btn {
            width: 100%;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 12px 16px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
            direction: rtl;
        }

        .sidebar-close-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 1);
        }

        .sidebar-close-btn svg {
            width: 14px;
            height: 14px;
        }

        .fixed-energy-title {
            position: fixed;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 12px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 50px;
            padding: 12px 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            pointer-events: none; /* avoid intercepting clicks below */
        }

        .energy-title-icon {
            font-size: 24px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 2px 4px rgba(255, 107, 53, 0.3));
        }

        .fixed-energy-title h1 {
            margin: 0;
            font-size: 20px;
            font-weight: 700;
            color: #ffffff;
            text-shadow: 0 2px 10px rgba(255, 255, 255, 0.3);
        }

        .new-chat-btn {
            background: linear-gradient(135deg, #1976d2, #ff7200);
            border: none;
            border-radius: 12px;
            padding: 12px 16px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 24px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .new-chat-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(25, 118, 210, 0.4);
        }

        .chat-history {
            flex: 1;
            overflow-y: auto;
        }

        .chat-category {
            font-size: 11px;
            font-weight: 600;
            letter-spacing: 0.08em;
            text-transform: uppercase;
            color: rgba(255, 255, 255, 0.45);
            margin: 20px 0 10px;
            padding: 0 4px;
        }

        .chat-category:first-of-type {
            margin-top: 0;
        }

        .chat-item-icon {
            width: 32px;
            height: 32px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.08);
            font-size: 16px;
            flex-shrink: 0;
        }

        .chat-item-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 4px;
        }

        .chat-item {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .chat-item:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .chat-item.active {
            background: rgba(25, 118, 210, 0.15);
            border-color: rgba(25, 118, 210, 0.3);
        }

        .chat-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .chat-preview {
            font-size: 12px;
            color: #888;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .modal-main-content {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: transparent;
            position: relative;
        }

        .tool-content-area {
            flex: 1;
            display: none;
            flex-direction: column;
            align-items: stretch;
            justify-content: flex-start;
            padding: 20px;
            gap: 16px;
            overflow-y: auto;
        }

        .tool-content-area.active {
            display: flex;
        }

        .tool-content-body {
            flex: 1;
            width: 100%;
            background: rgba(0, 0, 0, 0.35);
            border-radius: 20px;
            padding: 28px;
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.35);
            backdrop-filter: blur(22px);
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 20px;
            min-height: 0;
        }

        .tool-loading-state {
            display: none;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            gap: 12px;
            padding: 24px;
            color: rgba(255, 255, 255, 0.75);
            font-size: 14px;
        }

        .tool-loading-state.active {
            display: flex;
        }

        .tool-loading-spinner {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.2);
            border-top-color: rgba(255, 255, 255, 0.9);
            animation: toolSpinner 1s linear infinite;
        }

        @keyframes toolSpinner {
            to { transform: rotate(360deg); }
        }

        .tool-placeholder {
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            gap: 12px;
            color: rgba(255, 255, 255, 0.75);
            background: rgba(255, 255, 255, 0.04);
            border: 1px dashed rgba(255, 255, 255, 0.2);
            border-radius: 18px;
            padding: 32px;
        }

        .tool-placeholder.active {
            display: flex;
        }

        .tool-placeholder h3 {
            font-size: 20px;
            font-weight: 600;
        }

        .tool-placeholder p {
            font-size: 14px;
            line-height: 1.6;
            max-width: 420px;
        }

        .tool-section {
            background: rgba(10, 14, 25, 0.55);
            border-radius: 18px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.08);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.35);
        }

        .tool-section h3 {
            margin: 0 0 12px;
            font-size: 20px;
            font-weight: 600;
            color: #fff;
        }

        .tool-section p {
            margin: 0 0 12px;
            color: rgba(255, 255, 255, 0.75);
            line-height: 1.7;
        }

        .tool-grid {
            display: grid;
            gap: 16px;
        }

        @media (min-width: 900px) {
            .tool-grid.cols-2 {
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }

            .tool-grid.cols-3 {
                grid-template-columns: repeat(3, minmax(0, 1fr));
            }
        }

        .tool-input-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .tool-input-group label {
            font-size: 13px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.75);
        }

        .tool-input-group input,
        .tool-input-group select,
        .tool-input-group textarea {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 10px 12px;
            color: #fff;
            font-size: 14px;
            transition: border 0.2s ease, box-shadow 0.2s ease;
        }

        .tool-input-group input:focus,
        .tool-input-group select:focus,
        .tool-input-group textarea:focus {
            outline: none;
            border-color: rgba(255, 173, 76, 0.7);
            box-shadow: 0 0 0 3px rgba(255, 173, 76, 0.2);
        }

        .tool-action-btn {
            background: linear-gradient(135deg, #1c92ff, #ff7a18);
            border: none;
            border-radius: 14px;
            padding: 12px 16px;
            color: #fff;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .tool-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 16px 30px rgba(28, 146, 255, 0.35);
        }

        .tool-results-card {
            display: flex;
            flex-direction: column;
            gap: 10px;
            background: rgba(255, 255, 255, 0.06);
            border-radius: 16px;
            padding: 16px;
            border: 1px solid rgba(255, 255, 255, 0.08);
        }

        .tool-results-card h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #fff;
        }

        .tool-results-grid {
            display: grid;
            gap: 10px;
        }

        @media (min-width: 700px) {
            .tool-results-grid.cols-2 {
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }
        }

        .tool-stat {
            background: rgba(0, 0, 0, 0.45);
            border-radius: 14px;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.06);
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .tool-stat span:first-child {
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.08em;
            color: rgba(255, 255, 255, 0.5);
        }

        .tool-stat strong {
            font-size: 18px;
            color: #fff;
        }

        .tool-notice {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            background: rgba(255, 173, 76, 0.08);
            border: 1px solid rgba(255, 173, 76, 0.3);
            padding: 12px 16px;
            border-radius: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 13px;
            line-height: 1.6;
        }

        .tool-note-icon {
            font-size: 20px;
        }

        .live-sim-map-wrapper {
            background: rgba(0, 0, 0, 0.35);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.08);
            padding: 16px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .live-sim-map {
            width: 100%;
            height: 280px;
            border-radius: 14px;
            overflow: hidden;
        }

        .live-sim-map-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .live-sim-map-controls button {
            border: none;
            border-radius: 999px;
            padding: 8px 16px;
            font-size: 12px;
            font-weight: 600;
            background: rgba(255, 255, 255, 0.12);
            color: #fff;
            cursor: pointer;
            transition: transform 0.2s ease, background 0.2s ease;
        }

        .live-sim-map-controls button:hover {
            transform: translateY(-1px);
            background: rgba(255, 255, 255, 0.2);
        }

        .live-sim-map-status {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .live-sim-metrics {
            display: grid;
            gap: 16px;
        }

        @media (min-width: 680px) {
            .live-sim-metrics {
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }
        }

        @media (min-width: 1024px) {
            .live-sim-metrics {
                grid-template-columns: repeat(4, minmax(0, 1fr));
            }
        }

        .live-sim-card {
            display: flex;
            flex-direction: column;
            gap: 6px;
            background: rgba(255, 255, 255, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 16px;
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.04);
        }

        .live-sim-card .label {
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.08em;
            color: rgba(255, 255, 255, 0.55);
        }

        .live-sim-card strong {
            font-size: 20px;
            font-weight: 700;
            color: #ffffff;
        }

        .live-sim-card .sub {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.55);
        }

        .live-sim-analysis {
            margin-top: 12px;
            background: rgba(255, 255, 255, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 16px;
            padding: 16px;
            color: rgba(255, 255, 255, 0.85);
            line-height: 1.6;
        }

        .live-sim-chart-wrapper {
            background: rgba(0, 0, 0, 0.35);
            border-radius: 18px;
            padding: 16px;
            border: 1px solid rgba(255, 255, 255, 0.08);
        }

        .live-sim-chart-canvas {
            width: 100%;
            height: 280px;
            display: block;
            border-radius: 12px;
            background: rgba(0, 0, 0, 0.2);
        }

        .live-sim-chart-grid {
            display: grid;
            gap: 18px;
        }

        @media (min-width: 900px) {
            .live-sim-chart-grid {
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }
        }

        @media (min-width: 1360px) {
            .live-sim-chart-grid {
                grid-template-columns: repeat(3, minmax(0, 1fr));
            }
        }

        .live-sim-legend {
            margin-top: 12px;
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.75);
        }

        .live-sim-legend span {
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .live-sim-legend i {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 999px;
        }

        .live-sim-weather-grid {
            display: grid;
            gap: 14px;
        }

        @media (min-width: 640px) {
            .live-sim-weather-grid {
                grid-template-columns: repeat(3, minmax(0, 1fr));
            }
        }

        .live-sim-forecast-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
            margin-top: 12px;
        }

        .live-sim-forecast-table th,
        .live-sim-forecast-table td {
            padding: 8px 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            text-align: right;
        }

        .live-sim-forecast-table th {
            font-weight: 600;
            color: rgba(255, 255, 255, 0.85);
        }

        .live-sim-grid-2 {
            display: grid;
            gap: 12px;
        }

        @media (min-width: 600px) {
            .live-sim-grid-2 {
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }
        }

        .tool-table {
            width: 100%;
            border-collapse: collapse;
            border-spacing: 0;
        }

        .tool-table thead {
            background: rgba(255, 255, 255, 0.05);
        }

        .tool-table th,
        .tool-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            color: rgba(255, 255, 255, 0.85);
            font-size: 14px;
        }

        .tool-table th {
            font-weight: 600;
            color: #fff;
        }

        .tool-pill {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            border-radius: 9999px;
            padding: 6px 12px;
            font-size: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.12);
        }

        .live-chart {
            display: flex;
            align-items: flex-end;
            gap: 8px;
            height: 160px;
            padding-bottom: 12px;
        }

        .live-bar {
            position: relative;
            width: 14px;
            border-radius: 10px 10px 4px 4px;
            background: linear-gradient(180deg, rgba(28, 146, 255, 0.9), rgba(12, 52, 104, 0.5));
            display: flex;
            align-items: flex-end;
            justify-content: center;
        }

        .live-bar.load {
            background: linear-gradient(180deg, rgba(255, 123, 67, 0.9), rgba(104, 38, 12, 0.5));
        }

        .live-bar::after {
            content: attr(data-label);
            position: absolute;
            bottom: -26px;
            font-size: 10px;
            color: rgba(255, 255, 255, 0.6);
            transform: rotate(-45deg);
            white-space: nowrap;
        }

        .tool-upload {
            display: flex;
            flex-direction: column;
            gap: 12px;
            align-items: center;
            justify-content: center;
            border: 1px dashed rgba(255, 255, 255, 0.4);
            border-radius: 16px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.04);
            cursor: pointer;
        }

        .tool-upload input[type="file"] {
            display: none;
        }

        .tool-upload span {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
        }

        .tool-image-preview {
            width: 100%;
            max-height: 220px;
            object-fit: contain;
            border-radius: 16px;
            background: rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.08);
        }

        .tool-tag {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 10px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 9999px;
            font-size: 11px;
            color: rgba(255, 255, 255, 0.8);
        }

        .tool-flex-row {
            display: flex;
            gap: 14px;
            flex-wrap: wrap;
        }

        .modal-header {
            padding: 80px 32px 24px;
            border-bottom: none;
            background: transparent;
        }

        .modal-header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            color: white;
        }

        .modal-header p {
            color: #888;
            font-size: 14px;
        }

        .modal-chat-area {
            flex: 1;
            padding: 0;
            overflow-y: auto;
            scroll-behavior: smooth;
            display: flex;
            flex-direction: column;
            position: relative;
            width: 100%;
            height: calc(100vh - 120px);
            align-items: center;
            justify-content: center;
        }

        /* Add safe space under the fixed pill title inside modals */
        .energy-optimization-modal .modal-chat-area,
        .energy-security-modal .modal-chat-area,
        .cloud-energy-modal .modal-chat-area {
            padding-top: 110px; /* prevent Welcome/input from sitting under the title */
        }

        /* Reserve space for the fixed bottom input bar so messages do not hide underneath */
        .modal-chat-area { padding-bottom: 220px; }

        .chat-content-area {
            text-align: center;
            margin: auto;
            max-width: 800px;
            width: 90%;
            padding: 40px 20px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            overflow: visible; /* avoid nested scroll; let modal-chat-area scroll */
            min-height: 60vh;
        }

        /* Ensure messages never hide behind the bottom input bar */
        .chat-content-area,
        .messages-container,
        .messages-list {
            padding-bottom: 180px; /* space for fixed input bar */
        }

        .welcome-content {
            display: flex !important;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            position: relative;
            visibility: visible !important;
            opacity: 1 !important;
            z-index: 10;
        }

        .welcome-icon {
            display: none;
        }

        .welcome-title {
            display: block !important;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 32px;
            text-align: center;
            color: #ffffff;
            text-shadow: 0 4px 20px rgba(255, 255, 255, 0.4);
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* Force compact welcome title inside modals (avoid hero overrides) */
        .energy-optimization-modal .welcome-title,
        .energy-security-modal .welcome-title {
            font-size: 36px !important;
            margin-bottom: 32px !important;
            text-align: center !important;
            text-shadow: 0 4px 20px rgba(255, 255, 255, 0.4) !important;
        }

        .welcome-subtitle {
            color: #888;
            font-size: 16px;
            margin-bottom: 32px;
            line-height: 1.5;
        }

        /* Dedicated modal welcome title to avoid conflicts with page hero */
        .modal-welcome-title {
            display: block !important;
            font-size: 36px !important;
            font-weight: 700 !important;
            margin-bottom: 32px !important;
            text-align: center !important;
            color: #ffffff !important;
            text-shadow: 0 4px 20px rgba(255, 255, 255, 0.4) !important;
            visibility: visible !important;
            opacity: 1 !important;
        }



        .messages-container {
            display: none;
            flex-direction: column;
            gap: 32px;
            padding: 40px 20px;
            overflow: visible; /* avoid nested scroll; let modal-chat-area scroll */
            width: 100%;
            max-width: 900px;
            margin: 0 auto;
            flex: 1;
            justify-content: flex-start;
            align-items: center;
        }

        .message {
            display: flex;
            gap: 0px;
            max-width: 800px;
            width: 90%;
            margin: 0 auto; /* remove vertical space between message and reply */
            align-items: center;
            justify-content: center;
        }

        .message.user {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: none;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            flex-shrink: 0;
        }

        .message.ai .message-avatar {
            background: linear-gradient(135deg, #1976d2, #ff7200);
        }

        .message.user .message-avatar {
            background: rgba(255, 255, 255, 0.1);
        }

        .message-content {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 20px;
            padding: 24px 32px;
            line-height: 1.7;
            color: white;
            font-size: 15px;
            width: 100%;
            max-width: 700px;
            word-wrap: break-word;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            text-align: center;
            margin: 0 auto;
        }

        .message.user .message-content {
            background: rgba(25, 118, 210, 0.1);
            border-color: rgba(25, 118, 210, 0.2);
        }

        .modal-input-container {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
        }

        .modal-input-container.center-input {
            padding: 40px 20px;
            margin: 40px 0;
            position: relative;
        }

        .modal-input-container.bottom-input {
            padding: 24px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.02);
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .modal-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            background: rgba(20, 20, 20, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 30px;
            padding: 12px;
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            width: 100%;
            max-width: 700px;
            transition: all 0.3s ease;
        }

        .modal-input-field {
            flex: 1;
            background: transparent;
            border: none;
            resize: none;
            padding: 12px 16px;
            color: #ffffff;
            font-size: 15px;
            line-height: 1.4;
            outline: none;
            font-family: 'Noto Sans Arabic', 'Inter', 'Segoe UI', Tahoma, Arial, sans-serif;
            min-height: 20px;
            max-height: 120px;
            overflow-y: auto;
            direction: rtl;
            text-align: center;
        }

        .modal-input-field::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .modal-action-btn {
            background: transparent;
            border: none;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: rgba(255, 255, 255, 0.6);
            margin: 0 4px;
        }

        .modal-action-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
        }

        .modal-send-btn {
            background: rgba(25, 118, 210, 0.3);
            border: none;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: rgba(255, 255, 255, 0.9);
            margin-left: 4px;
        }

        .modal-send-btn:hover {
            background: rgba(25, 118, 210, 0.5);
            transform: scale(1.05);
        }

        .modal-action-btn {
            background: transparent;
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: rgba(255, 255, 255, 0.6);
        }

        .modal-action-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
        }

        .modal-send-btn {
            position: static;
            transform: none;
        }

        .modal-send-btn:hover {
            background: rgba(25, 118, 210, 0.3);
            color: rgba(255, 255, 255, 1);
            transform: scale(1.05);
        }


        @media (max-width: 768px) {
            .modal-container {
                flex-direction: column;
            }

            .modal-sidebar {
                width: 100%;
                height: auto;
                border-right: none;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }


        }
    </style>

    <!-- Critical CSS - Load immediately -->
    <link rel="stylesheet" href="css/glassmorphism.css">

    <link rel="stylesheet" href="css/welcome-screen.css">

    <!-- CSS Files - Load non-critical CSS asynchronously -->
    <link rel="preload" href="css/performance-optimizations.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="css/styles.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="css/space-background.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="stylesheet" href="css/icons.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="css/naya.css">
    <link rel="stylesheet" href="css/enhanced-map-animations.css">
    <link rel="stylesheet" href="css/neon-cursor.css">

    <!-- Fallback for browsers that don't support preload -->
    <noscript>
        <link rel="stylesheet" href="css/performance-optimizations.css">
        <link rel="stylesheet" href="css/styles.css">
        <link rel="stylesheet" href="css/space-background.css">
        <link rel="stylesheet" href="css/welcome-screen.css">
    </noscript>


    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>

    <!-- Leaflet Draw CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.css"
          crossorigin=""/>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Glassmorphism Background -->
    <div class="glassmorphism-bg"></div>

    <!-- Floating Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <!-- Welcome Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="welcome-container">
            <div class="welcome-logo-section">
                <div class="welcome-logo">
                    <div class="site-logo large-logo"></div>
                    <div class="logo-glow"></div>
                </div>
                <h1 class="welcome-title">Welcome to</h1>
                <h2 class="brand-name">Energy.AI</h2>
                <p class="welcome-subtitle">Smart Energy Solutions Powered by AI</p>
            </div>
            <div class="welcome-animation">
                <div class="energy-particles"></div>
                <div class="loading-progress">
                    <div class="progress-bar"></div>
                </div>
                <button class="skip-loading-btn" onclick="skipLoading()" style="
                    position: absolute;
                    bottom: 30px;
                    right: 30px;
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    color: white;
                    padding: 10px 20px;
                    border-radius: 25px;
                    cursor: pointer;
                    font-size: 14px;
                    transition: all 0.3s ease;
                    backdrop-filter: blur(10px);
                " onmouseover="this.style.background='rgba(255, 255, 255, 0.2)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'">
                    Skip ⏭️
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main">
        <nav class="navbar glass">
            <div class="nav-content">
                <a href="#home" class="nav-logo">
                    <div class="nav-logo-icon">⚡</div>
                    <span>Energy.AI</span>
                </a>
                <ul class="nav-menu" id="navMenu">
                    <li><a href="#home" class="nav-link active">Home</a></li>
                    <li><a href="#about" class="nav-link">About</a></li>
                    <li><a href="#service" class="nav-link">Services</a></li>
                    <li><a href="#design" class="nav-link">Design</a></li>
                    <li><a href="#contact" class="nav-link">Contact</a></li>
                </ul>
                <div class="nav-actions">
                    <button class="mobile-menu-toggle" id="mobileMenuToggle">
                        <ion-icon name="menu-outline"></ion-icon>
                    </button>
                </div>
            </div>
        </nav>
        <section class="hero" id="home">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="title-line-1">Web Design &</span>
                    <span class="title-line-2">Development</span>
                    <span class="title-line-3">Energy</span>
                </h1>
                <p class="hero-description">
                    AI is the spark igniting a new era of energy innovation<br>
                    powering tomorrow with<br>
                    intelligent solutions today
                </p>
                <div class="hero-actions">
                    <button class="btn btn-primary" id="joinBtn">
                        <ion-icon name="person-add-outline"></ion-icon>
                        Join Us
                    </button>
                    <button class="btn btn-secondary">Book Free Consultation</button>
                </div>
            </div>

            <!-- Floating Glass Elements -->
            <div class="floating-glass floating-glass-1"></div>
            <div class="floating-glass floating-glass-2"></div>
            <div class="floating-glass floating-glass-3"></div>
        </section>

        <!-- Auth Modal will be created by auth-system.js -->

        <!-- Fixed CTA Button -->
        <div class="fixed-cta-container">
            <button class="fixed-cta-btn" id="fixedCtaBtn">
                <ion-icon name="call-outline"></ion-icon>
                <span>Get Free Consultation</span>
            </button>
        </div>

        <!-- AI Chat Interface -->
        <div class="ai-chat-container" id="aiChatContainer">
            <div class="chat-header">
                <h3>Energy.AI Assistant</h3>
                <div class="chat-controls">
                    <button class="minimize-btn chat-control-btn" id="minimizeChatBtn">
                        <ion-icon name="remove-outline"></ion-icon>
                    </button>
                    <button class="close-btn chat-control-btn" id="closeChatBtn">
                        <ion-icon name="close-outline"></ion-icon>
                    </button>
                </div>
            </div>
            <div class="chat-messages" id="chatMessages">
                <div class="message bot">
                    <div class="avatar">
                        <div class="avatar-icon-wrapper">
                            <ion-icon name="analytics-outline"></ion-icon>
                        </div>
                    </div>
                    <div class="message-content">Hello, I'm Energy.AI Assistant</div>
                </div>
            </div>
            <div class="suggested-questions">
                <button class="question-btn">How can AI improve energy efficiency?</button>
                <button class="question-btn">What is renewable energy?</button>
                <button class="question-btn">How to reduce electricity bills?</button>
            </div>
            <div class="chat-input">
                <input type="text" id="userInput" placeholder="Type your message here..." aria-label="Message input">
                <button id="sendAttachmentBtn" class="chat-icon-btn" aria-label="Send attachment">
                    <ion-icon name="attach-outline"></ion-icon>
                </button>
                <button id="sendMessageBtn" class="chat-send-btn">
                    <ion-icon name="send-outline"></ion-icon>
                    <span>Send</span>
                </button>
            </div>
            <div class="typing-indicator" id="typingIndicator">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <section id="about" class="section">
            <h2>About Energy.AI</h2>
            <div class="cards-grid">
                <div class="glass-card">
                    <div class="card-icon">⚡</div>
                    <h3>Smart Energy Management</h3>
                    <p>AI-powered solutions to optimize energy consumption in real-time.</p>
                </div>
                <div class="glass-card">
                    <div class="card-icon">🌱</div>
                    <h3>Sustainable Solutions</h3>
                    <p>Eco-friendly approaches to energy production and distribution.</p>
                </div>
                <div class="glass-card">
                    <div class="card-icon">📊</div>
                    <h3>Data-Driven Insights</h3>
                    <p>Comprehensive analytics to make informed energy decisions.</p>
                </div>
            </div>
        </section>

        <section id="service" class="section">
            <h2>Our Services</h2>
            <div class="cards-grid">
                <div class="glass-card energy-optimization-card" onclick="openEnergyOptimization()">
                    <div class="card-icon">💡</div>
                    <h3>Optimizing PV with AI</h3>
                    <p>Our AI algorithms analyze your energy consumption patterns and suggest optimizations to reduce waste and cost.</p>
                    <div class="card-action">
                        <span class="action-text">Click to start AI consultation</span>
                        <span class="action-arrow">→</span>
                    </div>
                </div>

                <div class="glass-card energy-security-card" onclick="openEnergySecurity()">
                    <div class="card-icon">🛡️</div>
                    <h3>Optimizing Wind Energy with AI</h3>
                    <p>Protect your energy infrastructure with advanced threat detection and response systems.</p>
                    <div class="card-action">
                        <span class="action-text">Click to start AI security</span>
                        <span class="action-arrow">→</span>
                    </div>
                </div>
                <div class="glass-card cloud-energy-card" onclick="openCloudEnergy()">
                    <div class="card-icon">☁️</div>
                    <h3>Cloud Energy Management</h3>
                    <p>Access your energy data and controls from anywhere with our secure cloud-based platform.</p>
                    <div class="card-action">
                        <span class="action-text">Click to start AI cloud</span>
                        <span class="action-arrow">→</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Energy Optimization Modal -->
        <div class="energy-optimization-modal" id="energyOptimizationModal">
            <div class="modal-container">
                <div class="sidebar-hover-area"></div>

                <div class="modal-sidebar">
                    <div class="modal-logo">
                        <div class="modal-logo-icon">⚡</div>
                        <div class="modal-logo-text">Optimizing PV with AI</div>
                    </div>

                    <button class="new-chat-btn" onclick="startNewChat()">
                        <span>+</span>
                        New Chat
                    </button>

                    <div class="chat-history">
                        <div class="chat-item" data-tool="dashboard">
                            <div class="chat-item-header">
                                <div class="chat-item-icon">📊</div>
                                <div class="chat-title">لوحة التحكم</div>
                            </div>
                            <div class="chat-preview">نظرة عامة سريعة على أدوات Energy AI وخلاصات الأداء.</div>
                        </div>

                        <div class="chat-category">AI Tools</div>

                        <div class="chat-item active" data-tool="assistant">
                            <div class="chat-item-header">
                                <div class="chat-item-icon">🤖</div>
                                <div class="chat-title">المساعد الذكي</div>
                            </div>
                            <div class="chat-preview">محادثة فورية مع الذكاء الاصطناعي لأي سؤال هندسي أو مالي.</div>
                        </div>

                        <div class="chat-item" data-tool="live-simulation">
                            <div class="chat-item-header">
                                <div class="chat-item-icon">🌬️</div>
                                <div class="chat-title">المحاكاة الحية للأداء</div>
                            </div>
                            <div class="chat-preview">أنشئ توأمًا رقميًا وشاهد أداء نظامك لحظة بلحظة.</div>
                        </div>

                        <div class="chat-item" data-tool="design-optimizer">
                            <div class="chat-item-header">
                                <div class="chat-item-icon">🧮</div>
                                <div class="chat-title">حاسبة حجم النظام</div>
                            </div>
                            <div class="chat-preview">صمم النظام الأمثل وحلل الجدوى الفنية والمالية.</div>
                        </div>

                        <div class="chat-item" data-tool="field-inspector">
                            <div class="chat-item-header">
                                <div class="chat-item-icon">📷</div>
                                <div class="chat-title">المفتش الميداني</div>
                            </div>
                            <div class="chat-preview">حمّل صورة للألواح واترك الذكاء الاصطناعي يكتشف المشكلات.</div>
                        </div>

                        <div class="chat-category">Calculators</div>

                        <div class="chat-item" data-tool="string-configuration">
                            <div class="chat-item-header">
                                <div class="chat-item-icon">🔧</div>
                                <div class="chat-title">تهيئة السلاسل</div>
                            </div>
                            <div class="chat-preview">حدد أفضل ترتيب للوحدات الشمسية وتجنب أخطاء التوصيل.</div>
                        </div>

                        <div class="chat-item" data-tool="system-sizing">
                            <div class="chat-item-header">
                                <div class="chat-item-icon">📒</div>
                                <div class="chat-title">الحاسبة التقنية والمالية</div>
                            </div>
                            <div class="chat-preview">حدد حجم النظام، عدد الألواح، والسيناريو المالي لتدفقات النقد.</div>
                        </div>
                        <div class="chat-item" data-tool="area-calculator">
                            <div class="chat-item-header">
                                <div class="chat-item-icon">📐</div>
                                <div class="chat-title">حاسبة المساحة</div>
                            </div>
                            <div class="chat-preview">قدّر عدد الألواح الممكن تركيبها في المساحة المتاحة.</div>
                        </div>

                        <div class="chat-item" data-tool="battery-storage">
                            <div class="chat-item-header">
                                <div class="chat-item-icon">🔋</div>
                                <div class="chat-title">حاسبة البطاريات</div>
                            </div>
                            <div class="chat-preview">احسب سعة بنك البطاريات وعددها وطريقة توصيلها.</div>
                        </div>

                        <div class="chat-item" data-tool="wire-sizing">
                            <div class="chat-item-header">
                                <div class="chat-item-icon">⚡</div>
                                <div class="chat-title">حجم الأسلاك</div>
                            </div>
                            <div class="chat-preview">اختر مقاطع الأسلاك المناسبة لتحقيق أقصى كفاءة وأمان.</div>
                        </div>

                        <div class="chat-category">Data & Reports</div>

                        <div class="chat-item" data-tool="pricing">
                            <div class="chat-item-header">
                                <div class="chat-item-icon">💲</div>
                                <div class="chat-title">بيانات التسعير</div>
                            </div>
                            <div class="chat-preview">اطلع على أحدث أسعار المكونات في السوق الأردني.</div>
                        </div>

                        <div class="chat-item" data-tool="report">
                            <div class="chat-item-header">
                                <div class="chat-item-icon">📝</div>
                                <div class="chat-title">التقرير الشامل</div>
                            </div>
                            <div class="chat-preview">جمّع نتائجك في تقرير واحد جاهز للمشاركة مع العملاء.</div>
                        </div>
                    </div>

                    <!-- Close button in sidebar -->
                    <div class="sidebar-close-section">
                        <button class="sidebar-close-btn" onclick="closeEnergyOptimization()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                            إغلاق المحادثة
                        </button>
                    </div>
                </div>

                <div class="modal-main-content">
                    <!-- Fixed Energy AI Title -->
                    <div class="fixed-energy-title">
                        <div class="energy-title-icon">⚡</div>
                        <h1>Energy AI</h1>
                    </div>

                    <div class="modal-header">
                    </div>

                    <div class="modal-chat-area">
                        <div class="chat-content-area" id="chatContentArea">
                            <!-- Welcome content -->
                            <div class="welcome-content" id="welcomeContent">
                                <h2 class="modal-welcome-title">Welcome</h2>



                                <!-- Input in center when welcome is shown -->
                                <div class="modal-input-container center-input" id="centerInputContainer">
                                    <div class="modal-input-wrapper">
                                        <button class="modal-action-btn" title="إرفاق ملف">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M21.44 11.05L12.25 20.24C11.12 21.37 9.31 21.37 8.18 20.24C7.05 19.11 7.05 17.3 8.18 16.17L15.84 8.51C16.97 7.38 18.78 7.38 19.91 8.51C21.04 9.64 21.04 11.45 19.91 12.58L13.76 18.73C13.2 19.29 12.31 19.29 11.75 18.73C11.19 18.17 11.19 17.28 11.75 16.72L17.9 10.57" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </button>
                                        <button class="modal-action-btn" title="تسجيل صوتي">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                                <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                                                <path d="M5 10V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                                                <path d="M12 20V23M8 23H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                            </svg>
                                        </button>
                                        <textarea
                                            class="modal-input-field"
                                            id="modalMessageInput"
                                            placeholder="اسأل عن أي شيء..."
                                            rows="1"
                                        ></textarea>
                                        <button class="modal-send-btn" onclick="sendModalMessage()" title="إرسال">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Messages will be added directly here -->
                        </div>
                    </div>

                    <div class="tool-content-area" id="toolContentArea">
                        <div class="tool-loading-state" id="toolContentLoader">
                            <div class="tool-loading-spinner"></div>
                            <p>جاري تحميل الأداة المختارة...</p>
                        </div>
                        <div class="tool-placeholder" id="toolContentPlaceholder">
                            <h3>اضغط على أداة لبدء العمل</h3>
                            <p>سيتم فتح أدوات Energy AI الاحترافية داخل اللوحة الحالية دون مغادرتها.</p>
                        </div>
                        <div class="tool-content-body" id="toolContentBody"></div>
                    </div>

                    <!-- Input at bottom when chatting -->
                    <div class="modal-input-container bottom-input" id="bottomInputContainer" style="display: none;">
                        <div class="modal-input-wrapper">
                            <button class="modal-action-btn" title="إرفاق ملف">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.44 11.05L12.25 20.24C11.12 21.37 9.31 21.37 8.18 20.24C7.05 19.11 7.05 17.3 8.18 16.17L15.84 8.51C16.97 7.38 18.78 7.38 19.91 8.51C21.04 9.64 21.04 11.45 19.91 12.58L13.76 18.73C13.2 19.29 12.31 19.29 11.75 18.73C11.19 18.17 11.19 17.28 11.75 16.72L17.9 10.57" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <button class="modal-action-btn" title="تسجيل صوتي">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                    <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                                    <path d="M5 10V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                                    <path d="M12 20V23M8 23H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                </svg>
                            </button>
                            <textarea
                                class="modal-input-field"
                                id="modalMessageInputBottom"
                                placeholder="اسأل عن أي شيء..."
                                rows="1"
                            ></textarea>
                            <button class="modal-send-btn" onclick="sendModalMessageFromBottom()" title="إرسال">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>
                    </div>


                </div>
            </div>
        </div>



        <!-- Energy Security Modal (independent copy) -->
        <div class="energy-security-modal" id="energySecurityModal">
            <div class="modal-container">
                <div class="sidebar-hover-area"></div>

                <div class="modal-sidebar">
                    <div class="modal-logo">
                        <div class="modal-logo-icon">🛡️</div>
                        <div class="modal-logo-text">Optimizing Wind Energy with AI</div>
                    </div>

                    <button class="new-chat-btn" onclick="startNewChatSecurity()">
                        <span>+</span>
                        New Chat
                    </button>

                    <div class="chat-history">
                        <div class="chat-item active">
                            <div class="chat-title">Optimizing Wind Energy with AI</div>
                            <div class="chat-preview">Threat detection and response</div>
                        </div>
                        <div class="chat-item">
                            <div class="chat-title">Grid Protection</div>
                            <div class="chat-preview">Best practices for resilience</div>
                        </div>
                        <div class="chat-item">
                            <div class="chat-title">Device Hardening</div>
                            <div class="chat-preview">Secure IoT energy devices</div>
                        </div>
                    </div>

                    <div class="sidebar-close-section">
                        <button class="sidebar-close-btn" onclick="closeEnergySecurity()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                            إغلاق المحادثة
                        </button>
                    </div>
                </div>

                <div class="modal-main-content">
                    <div class="fixed-energy-title">
                        <div class="energy-title-icon">🛡️</div>
                        <h1>Energy.AI</h1>
                    </div>

                    <div class="modal-chat-area">
                        <div class="chat-content-area" id="chatContentAreaSec">
                            <div class="welcome-content" id="welcomeContentSec">
                                <h2 class="modal-welcome-title">Welcome</h2>

                                <div class="modal-input-container center-input" id="centerInputContainerSec">
                                    <div class="modal-input-wrapper">
                                        <button class="modal-action-btn" title="إرفاق ملف">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M21.44 11.05L12.25 20.24C11.12 21.37 9.31 21.37 8.18 20.24C7.05 19.11 7.05 17.3 8.18 16.17L15.84 8.51C16.97 7.38 18.78 7.38 19.91 8.51C21.04 9.64 21.04 11.45 19.91 12.58L13.76 18.73C13.2 19.29 12.31 19.29 11.75 18.73C11.19 18.17 11.19 17.28 11.75 16.72L17.9 10.57" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </button>
                                        <button class="modal-action-btn" title="تسجيل صوتي">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                                <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                                                <path d="M5 10V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                                                <path d="M12 20V23M8 23H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                            </svg>
                                        </button>
                                        <textarea class="modal-input-field" id="modalMessageInputSec" placeholder="اسأل عن أي شيء..." rows="1"></textarea>
                                        <button class="modal-send-btn" onclick="sendModalMessageSec()" title="إرسال">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-input-container bottom-input" id="bottomInputContainerSec" style="display: none;">
                        <div class="modal-input-wrapper">
                            <button class="modal-action-btn" title="إرفاق ملف">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.44 11.05L12.25 20.24C11.12 21.37 9.31 21.37 8.18 20.24C7.05 19.11 7.05 17.3 8.18 16.17L15.84 8.51C16.97 7.38 18.78 7.38 19.91 8.51C21.04 9.64 21.04 11.45 19.91 12.58L13.76 18.73C13.2 19.29 12.31 19.29 11.75 18.73C11.19 18.17 11.19 17.28 11.75 16.72L17.9 10.57" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <button class="modal-action-btn" title="تسجيل صوتي">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                    <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                                    <path d="M5 10V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                                    <path d="M12 20V23M8 23H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                </svg>
                            </button>
                            <textarea class="modal-input-field" id="modalMessageInputBottomSec" placeholder="اسأل عن أي شيء..." rows="1"></textarea>
                            <button class="modal-send-btn" onclick="sendModalMessageFromBottomSec()" title="إرسال">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- Cloud Energy Management Modal (independent copy) -->
        <div class="cloud-energy-modal" id="cloudEnergyModal">
            <div class="modal-container">
                <div class="sidebar-hover-area"></div>

                <div class="modal-sidebar">
                    <div class="modal-logo">
                        <div class="modal-logo-icon">☁️</div>
                        <div class="modal-logo-text">Cloud Energy Management</div>
                    </div>

                    <button class="new-chat-btn" onclick="startNewChatCloud()">
                        <span>+</span>
                        New Chat
                    </button>

                    <div class="chat-history">
                        <div class="chat-item active">
                            <div class="chat-title">Cloud Energy Management</div>
                            <div class="chat-preview">Manage and monitor in the cloud</div>
                        </div>
                        <div class="chat-item">
                            <div class="chat-title">Realtime Dashboards</div>
                            <div class="chat-preview">Live energy KPIs</div>
                        </div>
                        <div class="chat-item">
                            <div class="chat-title">Data Pipelines</div>
                            <div class="chat-preview">Ingest, store, and analyze</div>
                        </div>
                    </div>

                    <div class="sidebar-close-section">
                        <button class="sidebar-close-btn" onclick="closeCloudEnergy()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                            إغلاق المحادثة
                        </button>
                    </div>
                </div>

                <div class="modal-main-content">
                    <div class="fixed-energy-title">
                        <div class="energy-title-icon">☁️</div>
                        <h1>Energy AI</h1>
                    </div>

                    <div class="modal-chat-area">
                        <div class="chat-content-area" id="chatContentAreaCloud">
                            <div class="welcome-content" id="welcomeContentCloud">
                                <h2 class="modal-welcome-title">Welcome</h2>

                                <div class="modal-input-container center-input" id="centerInputContainerCloud">
                                    <div class="modal-input-wrapper">
                                        <button class="modal-action-btn" title="إرفاق ملف">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M21.44 11.05L12.25 20.24C11.12 21.37 9.31 21.37 8.18 20.24C7.05 19.11 7.05 17.3 8.18 16.17L15.84 8.51C16.97 7.38 18.78 7.38 19.91 8.51C21.04 9.64 21.04 11.45 19.91 12.58L13.76 18.73C13.2 19.29 12.31 19.29 11.75 18.73C11.19 18.17 11.19 17.28 11.75 16.72L17.9 10.57" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </button>
                                        <button class="modal-action-btn" title="تسجيل صوتي">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                                <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                                                <path d="M5 10V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                                                <path d="M12 20V23M8 23H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                            </svg>
                                        </button>
                                        <textarea class="modal-input-field" id="modalMessageInputCloud" placeholder="اسأل عن أي شيء..." rows="1"></textarea>
                                        <button class="modal-send-btn" onclick="sendModalMessageCloud()" title="إرسال">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-input-container bottom-input" id="bottomInputContainerCloud" style="display: none;">
                        <div class="modal-input-wrapper">
                            <button class="modal-action-btn" title="إرفاق ملف">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.44 11.05L12.25 20.24C11.12 21.37 9.31 21.37 8.18 20.24C7.05 19.11 7.05 17.3 8.18 16.17L15.84 8.51C16.97 7.38 18.78 7.38 19.91 8.51C21.04 9.64 21.04 11.45 19.91 12.58L13.76 18.73C13.2 19.29 12.31 19.29 11.75 18.73C11.19 18.17 11.19 17.28 11.75 16.72L17.9 10.57" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <button class="modal-action-btn" title="تسجيل صوتي">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                    <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                                    <path d="M5 10V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                                    <path d="M12 20V23M8 23H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                </svg>
                            </button>
                            <textarea class="modal-input-field" id="modalMessageInputBottomCloud" placeholder="اسأل عن أي شيء..." rows="1"></textarea>
                            <button class="modal-send-btn" onclick="sendModalMessageFromBottomCloud()" title="إرسال">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div id="design" class="section design-section">
            <h2>Our Design Approach</h2>
            <div class="design-gallery">
                <div class="design-card">
                    <div class="design-image">
                        <ion-icon name="home-outline"></ion-icon>
                    </div>
                    <h3>Smart Home Integration</h3>
                    <p>Seamless connection between your energy systems and smart home devices.</p>
                </div>

                <div class="design-card map-card">
                    <div class="design-image map-container">
                        <div id="embedded-map" class="embedded-map"></div>
                        <div class="map-overlay">
                            <h4>Energy.Ai Maps</h4>
                            <button class="map-expand-btn" id="expandMapBtn">
                                <ion-icon name="expand-outline"></ion-icon>
                                View Full Map
                            </button>
                        </div>
                    </div>
                    <h3>Energy.Ai Maps</h3>
                    <p>Interactive mapping interface with real-time energy data visualization and location-based analysis.</p>
                </div>
            </div>
        </div>

        <div id="contact" class="section contact-section">
            <div class="contact-header">
                <h2>Get In Touch</h2>
                <p class="contact-subtitle">Ready to transform your energy future? Let's start the conversation.</p>
            </div>

            <!-- Free Consultation Highlight Box -->
            <div class="consultation-highlight">
                <div class="consultation-content">
                    <div class="consultation-icon">
                        <ion-icon name="bulb-outline"></ion-icon>
                    </div>
                    <div class="consultation-text">
                        <h3>Get Your Free Energy Consultation</h3>
                        <p>Our experts will analyze your energy needs and provide customized solutions to reduce costs by 15-30%.</p>
                        <ul class="consultation-benefits">
                            <li>
                                <ion-icon name="checkmark-circle-outline"></ion-icon>
                                <span>Free energy audit and assessment</span>
                            </li>
                            <li>
                                <ion-icon name="checkmark-circle-outline"></ion-icon>
                                <span>Customized energy optimization plan</span>
                            </li>
                            <li>
                                <ion-icon name="checkmark-circle-outline"></ion-icon>
                                <span>ROI analysis and cost savings projection</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="contact-container">
                <div class="contact-form-wrapper">
                    <div class="contact-form">
                        <h3>Send us a Message</h3>
                        <form id="contactForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="contact-name">
                                        <ion-icon name="person-outline"></ion-icon>
                                        <span>Full Name</span>
                                    </label>
                                    <input type="text" id="contact-name" placeholder="Enter your full name" required>
                                </div>
                                <div class="form-group">
                                    <label for="contact-company">
                                        <ion-icon name="business-outline"></ion-icon>
                                        <span>Company</span>
                                    </label>
                                    <input type="text" id="contact-company" placeholder="Your company name">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="contact-email">
                                        <ion-icon name="mail-outline"></ion-icon>
                                        <span>Email Address</span>
                                    </label>
                                    <input type="email" id="contact-email" placeholder="<EMAIL>" required>
                                </div>
                                <div class="form-group">
                                    <label for="contact-phone">
                                        <ion-icon name="call-outline"></ion-icon>
                                        <span>Phone Number</span>
                                    </label>
                                    <input type="tel" id="contact-phone" placeholder="+962 XXX XXX XXX">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="contact-subject">
                                    <ion-icon name="chatbubble-outline"></ion-icon>
                                    <span>Subject</span>
                                </label>
                                <select id="contact-subject" required>
                                    <option value="">Select a topic</option>
                                    <option value="consultation" selected>Free Consultation</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="partnership">Partnership</option>
                                    <option value="support">Technical Support</option>
                                    <option value="demo">Request Demo</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="contact-message">
                                    <ion-icon name="document-text-outline"></ion-icon>
                                    <span>Message</span>
                                </label>
                                <textarea id="contact-message" rows="6" placeholder="Tell us about your energy needs and how we can help..." required></textarea>
                            </div>

                            <button type="submit" class="submit-btn" id="contactSubmitBtn">
                                <span class="btn-text">Send Message</span>
                                <ion-icon name="paper-plane-outline"></ion-icon>
                            </button>
                            <div id="contactFormStatus" class="form-status"></div>
                        </form>
                    </div>
                </div>

                <div class="contact-info-wrapper">
                    <div class="contact-info">
                        <h3>Contact Information</h3>
                        <p class="info-description">Connect with our energy experts today</p>

                        <div class="info-items">
                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="location-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Office Location</h4>
                                    <p>Amman, Jordan</p>
                                    <span>Middle East Headquarters</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="mail-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Email Address</h4>
                                    <p><EMAIL></p>
                                    <span>We reply within 24 hours</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="call-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Phone Number</h4>
                                    <p>+962 79 155 6430</p>
                                    <span>Mon - Fri, 9:00 AM - 6:00 PM</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="time-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Business Hours</h4>
                                    <p>Sunday - Thursday</p>
                                    <span>9:00 AM - 6:00 PM (GMT+3)</span>
                                </div>
                            </div>
                        </div>

                        <div class="contact-map">
                            <div class="map-placeholder">
                                <ion-icon name="map-outline"></ion-icon>
                                <p>Interactive Map</p>
                                <button class="map-btn" onclick="openContactMap()">View Location</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Map Modal -->
    <div class="map-modal" id="mapModal">
        <div class="map-modal-content">
            <div class="map-modal-header">
                <h3>Energy.Ai Maps - Interactive View</h3>
                <button class="map-close-btn" id="closeMapModal">
                    <ion-icon name="close-outline"></ion-icon>
                </button>
            </div>
            <div id="full-map" class="full-map"></div>
        </div>
    </div>

    <footer class="footer">
        <div class="footer-content">
            <div class="footer-logo">
                <div class="footer-brand-icon"></div>
                <h2>Energy.Ai</h2>
                <p>Powering the future with intelligent solutions</p>
            </div>
            <div class="footer-links">
                <h3>Links</h3>
                <ul>
                    <li><a href="#home">Home</a></li>
                    <li><a href="#about">About</a></li>
                    <li><a href="#service" id="footerServiceLink">Services</a></li>
                    <li><a href="#design">Design</a></li>
                    <li><a href="#contact">Contact</a></li>
                    <li><a href="privacy-policy.html">Privacy Policy</a></li>
                    <li><a href="faq.html">FAQ</a></li>
                </ul>
            </div>
            <div class="footer-social">
                <h3>Follow Us</h3>
                <div class="social-icons">
                    <a href="#" aria-label="Facebook"><ion-icon name="logo-facebook"></ion-icon></a>
                    <a href="#" aria-label="Instagram"><ion-icon name="logo-instagram"></ion-icon></a>
                    <a href="#" aria-label="Twitter"><ion-icon name="logo-twitter"></ion-icon></a>
                </div>
            </div>
            <div class="footer-newsletter">
                <h3>Newsletter</h3>
                <p>Stay updated with our latest news</p>
                <div class="newsletter-form">
                    <input type="email" placeholder="Your Email Address" aria-label="Email for newsletter">
                    <button aria-label="Subscribe">
                        <ion-icon name="paper-plane-outline"></ion-icon>
                    </button>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2025 Energy.AI. All Rights Reserved.</p>
        </div>
    </footer>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>

    <!-- Leaflet Draw JavaScript -->
    <script src="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.js"
            crossorigin=""></script>

    <!-- Leaflet GeometryUtil for area calculations -->
    <script src="https://unpkg.com/leaflet-geometryutil@0.10.1/src/leaflet.geometryutil.js"
            crossorigin=""></script>

    <!-- Ionicons -->
    <script type="module" src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"></script>

    <!-- Critical JavaScript - Load immediately -->
    <script>
        // Ultra-simple loading script - no complications
        console.log('ًںڑ€ Energy.AI Loading script started');
        console.log('📊 Document ready state:', document.readyState);

        let loadingHidden = false;

        function hideLoadingScreen() {
            console.log('🔄 hideLoadingScreen called, loadingHidden:', loadingHidden);
            if (loadingHidden) {
                console.log('⚠️ Loading already hidden, skipping');
                return;
            }
            loadingHidden = true;

            console.log('🎯 Hiding loading screen...');
            const loadingScreen = document.getElementById('loadingScreen');
            console.log('🔍 Loading screen element:', loadingScreen);

            if (loadingScreen) {
                console.log('âœ… Loading screen found, current display:', getComputedStyle(loadingScreen).display);
                console.log('âœ… Loading screen found, current opacity:', getComputedStyle(loadingScreen).opacity);
                loadingScreen.classList.add('hidden');
                console.log('🎨 Added hidden class to loading screen');

                setTimeout(() => {
                    if (loadingScreen && loadingScreen.parentNode) {
                        console.log('🗑️ Removing loading screen from DOM');
                        loadingScreen.style.display = 'none';
                        console.log('✅ Loading screen hidden successfully');
                    } else {
                        console.log('⚠️ Loading screen no longer in DOM');
                    }
                }, 800);
            } else {
                console.error('❌ Loading screen element not found in DOM');
                console.log('🔍 Available elements with loading-screen class:', document.querySelectorAll('.loading-screen'));
            }
        }

        // Make function available globally immediately
        window.hideLoadingScreen = hideLoadingScreen;
        console.log('🌐 hideLoadingScreen function made globally available');

        // Auto-hide after 3 seconds - no dependencies
        console.log('⏰ Setting up auto-hide timer for 3 seconds');
        setTimeout(function() {
            console.log('⏰ Auto-hide timer triggered after 3 seconds');
            hideLoadingScreen();
        }, 3000);

        // Backup: hide when page loads
        console.log('📄 Document ready state check:', document.readyState);
        if (document.readyState === 'loading') {
            console.log('📄 Document still loading, setting up load event listener');
            window.addEventListener('load', function() {
                console.log('📄 Window load event triggered');
                setTimeout(function() {
                    console.log('📄 Window load backup timer triggered');
                    hideLoadingScreen();
                }, 1000);
            });
        } else {
            console.log('📄 Document already loaded, setting immediate backup timer');
            setTimeout(function() {
                console.log('📄 Immediate backup timer triggered');
                hideLoadingScreen();
            }, 1000);
        }

        // Initialize other components safely
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded - initializing components');

            // Initialize welcome screen particles (optional)
            setTimeout(function() {
                try {
                    initializeWelcomeScreen();
                } catch (error) {
                    console.log('Welcome screen initialization skipped:', error.message);
                }
            }, 100);

            // Initialize sidebar (optional)
            setTimeout(function() {
                try {
                    setupEnergyToolSidebar();
                } catch (error) {
                    console.log('Energy tool sidebar setup skipped:', error.message);
                }
            }, 200);
        });

        // Skip loading function - simple and direct
        function skipLoading() {
            console.log('🔘 Skip loading button clicked by user');
            hideLoadingScreen();
        }

        // Make skipLoading globally available too
        window.skipLoading = skipLoading;

        function initializeWelcomeScreen() {
            try {
                // Add language attributes for translation
                const welcomeTitle = document.querySelector('.welcome-title');
                const brandName = document.querySelector('.brand-name');
                const welcomeSubtitle = document.querySelector('.welcome-subtitle');

                // Language attributes removed - English only

                // Add additional energy particles for enhanced visual effect
                createEnergyParticles();
            } catch (error) {
                console.log('Error in initializeWelcomeScreen:', error.message);
            }
        }

        function createEnergyParticles() {
            try {
                const particlesContainer = document.querySelector('.energy-particles');
                if (particlesContainer) {
                    // Create additional floating particles
                    for (let i = 0; i < 6; i++) {
                        const particle = document.createElement('div');
                        particle.className = 'floating-particle';
                        particle.style.cssText = `
                            position: absolute;
                            width: 3px;
                            height: 3px;
                            background: var(--secondary-color);
                            border-radius: 50%;
                            top: ${Math.random() * 100}%;
                            left: ${Math.random() * 100}%;
                            animation: particleFloat ${3 + Math.random() * 2}s ease-in-out infinite;
                            animation-delay: ${Math.random() * 2}s;
                            opacity: 0.7;
                        `;
                        particlesContainer.appendChild(particle);
                    }
                }
            } catch (error) {
                console.log('Error creating energy particles:', error.message);
            }
        }

        const toolRenderers = {
            'dashboard': renderToolDashboard,
            'live-simulation': renderToolLiveSimulation,
            'design-optimizer': renderToolDesignOptimizer,
            'field-inspector': renderToolFieldInspector,
            'string-configuration': renderToolStringConfiguration,
            'system-sizing': renderToolSystemSizing,
            'area-calculator': renderToolAreaCalculator,
            'battery-storage': renderToolBatteryStorage,
            'wire-sizing': renderToolWireSizing,
            'pricing': renderToolPricing,
            'report': renderToolReport,
        };

        let activeToolCleanup = null;
        let activeEnergyToolKey = null;

        const energyReport = (() => {
            const cards = [];

            function ensureId(card) {
                if (!card.id) {
                    card.id = `report-${Date.now()}-${Math.random().toString(16).slice(2, 8)}`;
                }
                return card.id;
            }

            function notify() {
                document.dispatchEvent(new CustomEvent('energy-report-update'));
            }

            function addCard(rawCard) {
                if (!rawCard || typeof rawCard !== 'object') {
                    return;
                }
                const card = { ...rawCard };
                ensureId(card);
                cards.push(card);
                notify();
            }

            function removeCard(id) {
                const index = cards.findIndex(card => card.id === id);
                if (index !== -1) {
                    cards.splice(index, 1);
                    notify();
                }
            }

            function clear() {
                if (cards.length) {
                    cards.splice(0, cards.length);
                    notify();
                }
            }

            function getCards() {
                return cards.map(card => ({ ...card }));
            }

            return { addCard, removeCard, clear, getCards };
        })();

        window.energyReport = energyReport;

        const WEATHER_API_KEY = 'fdca9387bb35438eaba110808251009';

        function parseNumber(value, fallback = 0) {
            if (value === null || value === undefined || value === '') return fallback;
            const parsed = Number(value);
            return Number.isFinite(parsed) ? parsed : fallback;
        }

        function formatNumber(value, fractionDigits = 2) {
            return Number(value).toLocaleString('ar-JO', {
                minimumFractionDigits: fractionDigits,
                maximumFractionDigits: fractionDigits,
            });
        }

        function calculateAdvancedStringConfigurationLocal(input) {
            const {
                vmp,
                voc,
                tempCoefficient,
                mpptMin,
                mpptMax,
                inverterMaxVolt,
                minTemp,
                maxTemp,
                targetSystemSize,
                panelWattage,
                isc,
            } = input;

            const STC_TEMP = 25;
            const tempCoeff = tempCoefficient / 100;

            const vocAtMinTemp = voc * (1 + (minTemp - STC_TEMP) * tempCoeff);
            const vmpAtMaxTemp = vmp * (1 + (maxTemp - STC_TEMP) * tempCoeff);

            const maxPanels = Math.floor(inverterMaxVolt / Math.max(vocAtMinTemp, 0.001));
            const minPanels = Math.ceil(mpptMin / Math.max(vmpAtMaxTemp, 0.001));

            const targetOptimalVoltage = (mpptMin + mpptMax) / 2;
            let optimalPanels = Math.round(targetOptimalVoltage / Math.max(vmp, 0.001));

            if (optimalPanels < minPanels) optimalPanels = minPanels;
            if (optimalPanels > maxPanels) optimalPanels = maxPanels;

            if (minPanels > maxPanels || optimalPanels <= 0) {
                return {
                    minPanels: minPanels > maxPanels ? minPanels : 0,
                    maxPanels,
                    optimalPanels: 0,
                    maxStringVocAtMinTemp: 0,
                    minStringVmpAtMaxTemp: 0,
                    arrayConfig: { totalPanels: 0, parallelStrings: 0, totalCurrent: 0 },
                };
            }

            const totalPanels = Math.ceil((targetSystemSize * 1000) / Math.max(panelWattage, 1));
            const parallelStrings = Math.max(1, Math.ceil(totalPanels / Math.max(optimalPanels, 1)));
            const totalCurrent = parallelStrings * isc * 1.25;

            const maxStringVocAtMinTemp = maxPanels * vocAtMinTemp;
            const minStringVmpAtMaxTemp = minPanels * vmpAtMaxTemp;

            return {
                minPanels,
                maxPanels,
                optimalPanels,
                maxStringVocAtMinTemp,
                minStringVmpAtMaxTemp,
                arrayConfig: {
                    totalPanels,
                    parallelStrings,
                    totalCurrent: parseFloat(totalCurrent.toFixed(2)),
                },
            };
        }

        function calculateAreaProductionLocal(input) {
            const SPACING_FACTOR = 1.5;

            const rowsPortrait = Math.floor(input.landLength / Math.max(input.panelLength * SPACING_FACTOR, 0.01));
            const panelsPerRowPortrait = Math.floor(input.landWidth / Math.max(input.panelWidth, 0.01));
            const totalPanelsPortrait = rowsPortrait * panelsPerRowPortrait;

            const rowsLandscape = Math.floor(input.landLength / Math.max(input.panelWidth * SPACING_FACTOR, 0.01));
            const panelsPerRowLandscape = Math.floor(input.landWidth / Math.max(input.panelLength, 0.01));
            const totalPanelsLandscape = rowsLandscape * panelsPerRowLandscape;

            let maxPanels;
            let finalOrientation;
            let panelsPerString;
            let rowCount;

            if (input.orientation === 'portrait') {
                maxPanels = totalPanelsPortrait;
                finalOrientation = 'portrait';
                panelsPerString = panelsPerRowPortrait;
                rowCount = rowsPortrait;
            } else if (input.orientation === 'landscape') {
                maxPanels = totalPanelsLandscape;
                finalOrientation = 'landscape';
                panelsPerString = panelsPerRowLandscape;
                rowCount = rowsLandscape;
            } else {
                if (totalPanelsPortrait >= totalPanelsLandscape) {
                    maxPanels = totalPanelsPortrait;
                    finalOrientation = 'portrait';
                    panelsPerString = panelsPerRowPortrait;
                    rowCount = rowsPortrait;
                } else {
                    maxPanels = totalPanelsLandscape;
                    finalOrientation = 'landscape';
                    panelsPerString = panelsPerRowLandscape;
                    rowCount = rowsLandscape;
                }
            }

            const totalPowerKw = (maxPanels * input.panelWattage) / 1000;
            const sunHours = Math.max(0, input.sunHours);
            const dailyEnergyKwh = totalPowerKw * sunHours;
            const monthlyEnergyKwh = dailyEnergyKwh * 30;
            const yearlyEnergyKwh = dailyEnergyKwh * 365;

            return {
                maxPanels,
                totalPowerKw,
                dailyEnergyKwh,
                monthlyEnergyKwh,
                yearlyEnergyKwh,
                finalOrientation,
                panelsPerString,
                rowCount,
            };
        }

        function calculateBatteryBankLocal(input) {
            const appliances = Array.isArray(input.appliances) ? input.appliances : [];
            let finalDailyLoadKwh = input.dailyLoadKwh;
            if (appliances.length) {
                const applianceLoad = appliances.reduce((total, appliance) => {
                    const power = Number(appliance.power) || 0;
                    const quantity = Number(appliance.quantity) || 0;
                    const hours = Number(appliance.hours) || 0;
                    if (power > 0 && quantity > 0 && hours > 0) {
                        return total + (power * quantity * hours) / 1000;
                    }
                    return total;
                }, 0);
                if (applianceLoad > 0) {
                    finalDailyLoadKwh = applianceLoad;
                }
            }

            const dodFactor = Math.max(0.01, input.depthOfDischarge / 100);
            const requiredBankEnergyKwh = (finalDailyLoadKwh * input.autonomyDays) / dodFactor;
            const requiredBankCapacityAh = (requiredBankEnergyKwh * 1000) / Math.max(input.systemVoltage, 0.1);
            const batteriesInSeries = Math.max(1, Math.round(input.systemVoltage / Math.max(input.batteryVoltage, 0.1)));
            const parallelStrings = Math.max(1, Math.ceil(requiredBankCapacityAh / Math.max(input.batteryCapacityAh, 0.1)));
            const totalBatteries = batteriesInSeries * parallelStrings;

            return {
                requiredBankEnergyKwh: parseFloat(requiredBankEnergyKwh.toFixed(2)),
                requiredBankCapacityAh: parseFloat(requiredBankCapacityAh.toFixed(2)),
                batteriesInSeries,
                parallelStrings,
                totalBatteries,
                finalDailyLoadKwh: parseFloat(finalDailyLoadKwh.toFixed(2)),
            };
        }

        function calculateWireSizeLocal(input) {
            const copperResistivity = 0.0172;
            const maxVoltageDrop = input.voltage * (input.voltageDropPercentage / 100);
            const calculatedArea = (2 * copperResistivity * input.distance * input.current) / Math.max(maxVoltageDrop, 0.01);
            const standardSizesMM2 = [1.5, 2.5, 4, 6, 10, 16, 25, 35, 50, 70, 95, 120];
            const recommendedWireSizeMM2 = standardSizesMM2.find(size => size >= calculatedArea) || standardSizesMM2[standardSizesMM2.length - 1];
            const actualVoltageDrop = (2 * copperResistivity * input.distance * input.current) / recommendedWireSizeMM2;
            const powerLoss = actualVoltageDrop * input.current;

            return {
                recommendedWireSizeMM2: parseFloat(recommendedWireSizeMM2.toFixed(2)),
                voltageDrop: parseFloat(actualVoltageDrop.toFixed(2)),
                powerLoss: parseFloat(powerLoss.toFixed(2)),
            };
        }

        function calculateDesignOptimizationLocal(input) {
            const sunHoursMap = { amman: 5.5, zarqa: 5.6, irbid: 5.4, aqaba: 6.0 };
            const monthlySunHours = monthlyPSSH[input.location] || monthlyPSSH['amman'];

            const monthlyConsumption = input.calculationMode === 'bill'
                ? (input.kwhPrice > 0 ? input.monthlyBill / input.kwhPrice : 0)
                : input.monthlyConsumption;
            const safeMonthlyConsumption = Math.max(0, monthlyConsumption);
            const annualConsumption = safeMonthlyConsumption * 12;
            const dailyConsumption = safeMonthlyConsumption / 30;

            const sunHours = sunHoursMap[input.location] || 5.5;
            const systemLossFactor = Math.max(0, (100 - input.systemLoss) / 100);

            const consumptionBasedSize = sunHours > 0 && systemLossFactor > 0
                ? dailyConsumption / (sunHours * systemLossFactor)
                : 0;

            const panelAreaWithSpacing = (1.13 * 2.28) * 1.5;
            const maxPanelsFromArea = Math.max(0, Math.floor(input.surfaceArea / Math.max(panelAreaWithSpacing, 0.1)));
            const areaBasedSize = (maxPanelsFromArea * input.panelWattage) / 1000;

            let limitingFactor;
            let optimizedSystemSize;
            if (consumptionBasedSize <= 0 && areaBasedSize <= 0) {
                return null;
            }
            if (areaBasedSize === 0 || (consumptionBasedSize > 0 && consumptionBasedSize <= areaBasedSize)) {
                limitingFactor = 'consumption';
                optimizedSystemSize = consumptionBasedSize;
            } else {
                limitingFactor = 'area';
                optimizedSystemSize = areaBasedSize;
            }

            if (!Number.isFinite(optimizedSystemSize) || optimizedSystemSize <= 0) {
                optimizedSystemSize = Math.max(consumptionBasedSize, areaBasedSize);
                limitingFactor = optimizedSystemSize === consumptionBasedSize ? 'consumption' : 'area';
            }

            const panelCount = Math.max(1, Math.ceil((optimizedSystemSize * 1000) / Math.max(input.panelWattage, 1)));
            const totalDcPower = parseFloat(((panelCount * input.panelWattage) / 1000).toFixed(2));
            const requiredArea = parseFloat((panelCount * panelAreaWithSpacing).toFixed(2));

            const inverterSize = parseFloat((totalDcPower * 0.9).toFixed(2));
            const phase = totalDcPower > 6 ? 'ثلاثي الطور' : 'أحادي الطور';

            const panelsPerString = panelCount <= 22 ? panelCount : Math.ceil(panelCount / Math.ceil(panelCount / 22));
            const parallelStrings = Math.max(1, Math.ceil(panelCount / Math.max(panelsPerString, 1)));

            const annualProduction = totalDcPower * sunHours * 365 * systemLossFactor;
            const usableAnnualProduction = Math.min(annualProduction, annualConsumption);
            const annualRevenue = usableAnnualProduction * input.kwhPrice;

            const totalInvestment = totalDcPower * input.costPerKw;
            const annualOMCost = totalInvestment * (input.omPercent / 100);

            const netAnnualCash = annualRevenue - annualOMCost;
            const paybackYears = netAnnualCash > 0 ? totalInvestment / netAnnualCash : null;
            const paybackMonths = paybackYears ? paybackYears * 12 : null;

            const degradationFactor = 1 - (input.degradationRate / 100);
            const inflationFactor = 1 + (input.inflation / 100);
            const projectLife = Math.max(1, Math.round(input.projectLife));

            let cumulative = -totalInvestment;
            let breakevenYear = null;
            const cashFlowAnalysis = [];
            for (let year = 1; year <= projectLife; year++) {
                const production = annualProduction * Math.pow(degradationFactor, year - 1);
                const usableProductionYear = Math.min(production, annualConsumption);
                const tariffYear = input.kwhPrice * Math.pow(inflationFactor, year - 1);
                const revenue = usableProductionYear * tariffYear;
                const omCost = annualOMCost * Math.pow(inflationFactor, year - 1);
                const netCash = revenue - omCost;
                cumulative += netCash;
                if (breakevenYear === null && cumulative >= 0) {
                    breakevenYear = year;
                }
                cashFlowAnalysis.push({
                    year,
                    production,
                    revenue,
                    omCost,
                    netCash,
                    cumulative,
                });
            }

            const finalCumulative = cashFlowAnalysis.length
                ? cashFlowAnalysis[cashFlowAnalysis.length - 1].cumulative
                : -totalInvestment;

            const monthlyBreakdown = monthlySunHours.map((sun, index) => {
                const production = totalDcPower * sun * daysInMonth[index] * systemLossFactor;
                const revenue = Math.min(production, annualConsumption / 12) * input.kwhPrice;
                return {
                    month: monthNames[index],
                    sunHours: sun,
                    production,
                    revenue,
                };
            });

            const reasoning = limitingFactor === 'area'
                ? 'تم تحديد حجم النظام وفق المساحة المتاحة. لزيادة القدرة يمكن التفكير في ألواح أعلى كفاءة أو مساحة إضافية.'
                : 'يتم حساب الحجم بناءً على استهلاكك الشهري مع مراعاة خسائر النظام ومتوسط ساعات الشمس المتاحة.';

            return {
                limitingFactor,
                reasoning,
                panelConfig: {
                    panelCount,
                    panelWattage: input.panelWattage,
                    totalDcPower,
                    requiredArea,
                },
                inverterConfig: {
                    recommendedSize: `${inverterSize.toFixed(1)} ك.و`,
                    phase,
                    mpptVoltage: '200 - 800 فولت',
                },
                wiringConfig: {
                    panelsPerString,
                    parallelStrings,
                    wireSize: 6,
                },
                financial: {
                    totalInvestment,
                    annualRevenue,
                    annualOMCost,
                    totalAnnualProduction: annualProduction,
                    netProfit: finalCumulative,
                    paybackPeriodYears: paybackYears,
                    paybackPeriodMonths: paybackMonths,
                    cashFlowAnalysis,
                    monthlyBreakdown,
                    breakevenYear,
                },
                inputs: {
                    annualConsumption,
                    sunHours,
                },
            };
        }
        async function fetchStringConfigurationReasoning(input, calc) {
            try {
                const chatEndpoint = window.configSystem?.getAPIEndpoint?.('chat') || '/api/chat';

                const payload = {
                    temperature: 0.3,
                    max_tokens: 400,
                    messages: [
                        {
                            role: 'system',
                            content: 'أنت مهندس طاقة شمسية تشرح نتائج حساب تهيئة السلاسل بلغة عربية واضحة ومختصرة.'
                        },
                        {
                            role: 'user',
                            content: `لدينا نظام طاقة شمسية بهذه البيانات:
- نطاق MPPT للعاكس: ${input.mpptMin}V إلى ${input.mpptMax}V
- أقصى جهد للعاكس: ${input.inverterMaxVolt}V
- Voc للوح: ${input.voc}V، Vmp: ${input.vmp}V
- معامل الحرارة للجهد: ${input.tempCoefficient}% لكل درجة
- درجات الحرارة المتوقعة: من ${input.minTemp}°C إلى ${input.maxTemp}°C

نتائج الحساب الفيزيائي:
- الحد الأقصى الآمن لعدد الألواح في السلسلة: ${calc.maxPanels}
- الحد الأدنى التشغيلي لعدد الألواح في السلسلة: ${calc.minPanels}
- العدد الموصى به: ${calc.optimalPanels}
- جهد السلسلة عند أبرد درجة (${input.minTemp}°C): ${calc.maxStringVocAtMinTemp.toFixed(2)}V
- جهد السلسلة عند أحر درجة (${input.maxTemp}°C): ${calc.minStringVmpAtMaxTemp.toFixed(2)}V

اكتب شرحاً من ثلاث فقرات قصيرة يوضح:
1. لماذا لا يمكن تجاوز ${calc.maxPanels} لوح (الحد الأقصى).
2. لماذا يجب ألا يقل العدد عن ${calc.minPanels} (الحد الأدنى).
3. لماذا يعد ${calc.optimalPanels} لوحاً هو الخيار الأفضل لمعظم الظروف.
استخدم لغة مهنية سهلة، واذكر الأرقام داخل الشرح.`
                        }
                    ],
                };

                const response = await fetch(chatEndpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload),
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                return data?.choices?.[0]?.message?.content || '';
            } catch (error) {
                console.error('Error in fetchStringConfigurationReasoning:', error);
                throw error; // Re-throw to be handled by calling function
            }
        }

        function setActiveEnergyToolItem(toolKey) {
            const modal = document.getElementById('energyOptimizationModal');
            if (!modal) {
                return;
            }

            const items = document.querySelectorAll('#energyOptimizationModal .chat-item[data-tool]');
            items.forEach(item => {
                if (item.dataset.tool === toolKey) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
        }

        function hideToolInterface() {
            const toolArea = document.getElementById('toolContentArea');
            const loader = document.getElementById('toolContentLoader');
            const placeholder = document.getElementById('toolContentPlaceholder');
            const body = document.getElementById('toolContentBody');

            if (typeof activeToolCleanup === 'function') {
                try { activeToolCleanup(); } catch (error) { console.warn('Tool cleanup failed', error); }
            }
            activeToolCleanup = null;

            if (toolArea) {
                toolArea.classList.remove('active');
            }
            if (loader) {
                loader.classList.remove('active');
            }
            if (placeholder) {
                placeholder.classList.remove('active');
            }
            if (body) {
                body.innerHTML = '';
            }
        }

        function showChatInterface(resetToWelcome = false) {
            const chatArea = document.querySelector('#energyOptimizationModal .modal-chat-area');
            const bottomInput = document.getElementById('bottomInputContainer');
            const centerInput = document.getElementById('centerInputContainer');
            const welcomeContent = document.getElementById('welcomeContent');
            const placeholder = document.getElementById('toolContentPlaceholder');

            hideToolInterface();

            if (chatArea) {
                chatArea.style.display = 'flex';
            }

            if (placeholder) {
                placeholder.classList.remove('active');
            }

            if (resetToWelcome) {
                if (centerInput) {
                    centerInput.style.display = 'flex';
                    centerInput.style.visibility = 'visible';
                    centerInput.style.opacity = '1';
                }
                if (bottomInput) {
                    bottomInput.style.display = 'none';
                }
                if (welcomeContent) {
                    welcomeContent.style.display = 'flex';
                    welcomeContent.style.visibility = 'visible';
                    welcomeContent.style.opacity = '1';
                }
            } else {
                if (bottomInput) {
                    bottomInput.style.display = energyModalConversation.length ? 'flex' : bottomInput.style.display;
                }
                if (!energyModalConversation.length && centerInput) {
                    centerInput.style.display = 'flex';
                    centerInput.style.visibility = 'visible';
                    centerInput.style.opacity = '1';
                }
                if (!energyModalConversation.length && welcomeContent) {
                    welcomeContent.style.display = 'flex';
                    welcomeContent.style.visibility = 'visible';
                    welcomeContent.style.opacity = '1';
                }
            }
        }

        function renderTool(toolKey, labelText) {
            const body = document.getElementById('toolContentBody');
            const loader = document.getElementById('toolContentLoader');
            const placeholder = document.getElementById('toolContentPlaceholder');

            if (!body) {
                return;
            }

            body.innerHTML = '';
            activeEnergyToolKey = toolKey;

            const renderer = toolRenderers[toolKey];

            if (!renderer) {
                if (loader) loader.classList.remove('active');
                if (placeholder) {
                    placeholder.innerHTML = `
                        <h3>${labelText || 'الأداة غير متاحة'}</h3>
                        <p>لم يتم دمج هذه الأداة داخل الواجهة حتى الآن. يرجى اختيار أداة أخرى.</p>
                    `;
                    placeholder.classList.add('active');
                }
                return;
            }

            try {
                const cleanup = renderer(body, labelText);
                activeToolCleanup = typeof cleanup === 'function' ? cleanup : null;
            } catch (error) {
                console.error('Failed to render tool', toolKey, error);
                if (placeholder) {
                    placeholder.innerHTML = `
                        <h3>تعذر تحميل ${labelText || 'الأداة'}</h3>
                        <p>حدث خطأ غير متوقع أثناء تشغيل هذه الأداة. يرجى إعادة المحاولة أو اختيار خيار آخر.</p>
                    `;
                    placeholder.classList.add('active');
                }
            } finally {
                if (loader) loader.classList.remove('active');
            }
        }

        function showToolInterface(toolKey, labelText) {
            const chatArea = document.querySelector('#energyOptimizationModal .modal-chat-area');
            const toolArea = document.getElementById('toolContentArea');
            const loader = document.getElementById('toolContentLoader');
            const placeholder = document.getElementById('toolContentPlaceholder');
            const bottomInput = document.getElementById('bottomInputContainer');
            const centerInput = document.getElementById('centerInputContainer');
            const body = document.getElementById('toolContentBody');

            if (!toolArea || !body) {
                return;
            }

            if (chatArea) {
                chatArea.style.display = 'none';
            }
            if (bottomInput) {
                bottomInput.style.display = 'none';
            }
            if (centerInput) {
                centerInput.style.display = 'none';
            }

            toolArea.classList.add('active');

            if (placeholder) {
                placeholder.classList.remove('active');
            }

            if (loader) {
                loader.classList.add('active');
            }

            // Slight delay to let loader show before heavy rendering
            requestAnimationFrame(() => {
                renderTool(toolKey, labelText);
            });
        }

        function setupEnergyToolSidebar() {
            // Check if the modal exists first
            const modal = document.getElementById('energyOptimizationModal');
            if (!modal) {
                console.log('Energy optimization modal not found, skipping sidebar setup');
                return;
            }

            const items = document.querySelectorAll('#energyOptimizationModal .chat-item[data-tool]');
            if (!items.length) {
                console.log('No tool items found in energy optimization modal');
                return;
            }

            items.forEach(item => {
                item.addEventListener('click', () => {
                    const toolKey = item.dataset.tool;
                    if (!toolKey) {
                        return;
                    }

                    setActiveEnergyToolItem(toolKey);

                    if (toolKey === 'assistant') {
                        showChatInterface(false);
                        return;
                    }

                    const labelText = item.querySelector('.chat-title')?.textContent?.trim();
                    showToolInterface(toolKey, labelText);
                });
            });
        }

        function renderToolDashboard(container) {
            const now = new Date();
            const formatter = new Intl.DateTimeFormat('ar-JO', { dateStyle: 'full', timeStyle: 'short' });

            container.innerHTML = `
                <div class="tool-section">
                    <h3>لوحة التحكم العامة</h3>
                    <p>نظرة سريعة على أداء أدوات Energy AI داخل هذه المساحة. اختر أي أداة من الشريط الجانبي لبدء العمل التفصيلي.</p>
                    <div class="tool-results-grid cols-2">
                        <div class="tool-stat"><span>الأدوات الذكية</span><strong>4</strong></div>
                        <div class="tool-stat"><span>الحاسبات الفنية</span><strong>4</strong></div>
                        <div class="tool-stat"><span>بيانات وتسعير</span><strong>2</strong></div>
                        <div class="tool-stat"><span>آخر تحديث</span><strong>${formatter.format(now)}</strong></div>
                    </div>
                </div>

                <div class="tool-section">
                    <h3>اقتراحات سريعة</h3>
                    <div class="tool-grid cols-2">
                        <div class="tool-results-card">
                            <h4>ابحث عن مقطع السلك المناسب</h4>
                            <p>حدد طول المسار وشدة التيار وسنقترح مساحة المقطع وأقرب معيار AWG.</p>
                            <button class="tool-action-btn" data-target="wire-sizing">ابدأ حاسبة الأسلاك</button>
                        </div>
                        <div class="tool-results-card">
                            <h4>صمم بنك البطاريات</h4>
                            <p>أدخل الاستهلاك اليومي وعدد أيام الاستقلالية للوصول إلى سعة البطاريات المناسبة.</p>
                            <button class="tool-action-btn" data-target="battery-storage">ابدأ التخزين</button>
                        </div>
                    </div>
                    <div class="tool-notice">
                        <span class="tool-note-icon">💡</span>
                        <div>يمكنك في أي وقت الانتقال مباشرة إلى أي أداة من الشريط الجانبي أو من هذه الاقتراحات السريعة دون مغادرة الواجهة الحالية.</div>
                    </div>
                </div>
            `;

            container.querySelectorAll('.tool-action-btn[data-target]').forEach(btn => {
                btn.addEventListener('click', (event) => {
                    const target = event.currentTarget.getAttribute('data-target');
                    if (target) {
                        setActiveEnergyToolItem(target);
                        showToolInterface(target, event.currentTarget.parentElement?.querySelector('h4')?.textContent || '');
                    }
                });
            });
        }

        function renderToolLiveSimulation(container) {
            container.innerHTML = `
                <div class="tool-section">
                    <h3>المحاكاة الحية للأداء</h3>
                    <p>بعد إدخال مواصفات النظام، ستحصل على قياسات لحظية تقارن بين الإنتاج الفعلي، والإنتاج المتوقع، والإنتاج المثالي تحت سماء صافية، مع تحديثات مستمرة خلال اليوم.</p>
                    <div class="live-sim-map-wrapper">
                        <div class="live-sim-map" id="liveSimMap"></div>
                        <div class="live-sim-map-controls">
                            <button type="button" id="liveSimUseLocation">استخدام موقعي الحالي</button>
                            <button type="button" id="liveSimResetLocation">إعادة تعيين الموقع</button>
                            <span class="live-sim-map-status" id="liveSimMapStatus">انقر على الخريطة لتحديث الإحداثيات.</span>
                        </div>
                    </div>
                    <form id="liveSimulationForm" class="tool-grid cols-3">
                        <div class="tool-input-group"><label>حجم النظام (kWp)</label><input type="number" name="systemSize" value="5" step="0.1" required></div>
                        <div class="tool-input-group"><label>سعر الكيلوواط/ساعة (دينار)</label><input type="number" name="kwhPrice" value="0.12" step="0.01" required></div>
                        <div class="tool-input-group"><label>زاوية ميل الألواح (°)</label><input type="number" name="panelTilt" value="30" step="1" required></div>
                        <div class="tool-input-group"><label>زاوية اتجاه الألواح (°)</label><input type="number" name="panelAzimuth" value="180" step="1" required></div>
                        <div class="tool-input-group"><label>خط العرض</label><input type="number" name="latitude" value="31.9539" step="0.0001" required></div>
                        <div class="tool-input-group"><label>خط الطول</label><input type="number" name="longitude" value="35.9106" step="0.0001" required></div>
                        <button type="submit" class="tool-action-btn live-sim-submit" data-mode="start">بدء المحاكاة والتحليل</button>
                    </form>
                    <div class="tool-notice" id="liveSimStatus" style="display:none;"></div>
                </div>
                <div class="tool-section" id="liveSimMetricsSection" style="display:none;">
                    <div class="live-sim-metrics" id="liveSimMetrics">
                        <div class="live-sim-card">
                            <span class="label">الإنتاج الحالي</span>
                            <strong id="liveSimLivePower">0</strong>
                            <span class="sub">واط</span>
                        </div>
                        <div class="live-sim-card">
                            <span class="label">التوقع اللحظي</span>
                            <strong id="liveSimForecastPower">0</strong>
                            <span class="sub">واط</span>
                        </div>
                        <div class="live-sim-card">
                            <span class="label">الإنتاج المثالي</span>
                            <strong id="liveSimIdealPower">0</strong>
                            <span class="sub">واط</span>
                        </div>
                        <div class="live-sim-card">
                            <span class="label">الكفاءة الحالية</span>
                            <strong id="liveSimPerformancePercent">0%</strong>
                            <span class="sub">مقارنة بالظروف المثالية</span>
                        </div>
                        <div class="live-sim-card">
                            <span class="label">التوفير الفوري</span>
                            <strong id="liveSimSavings">0.000</strong>
                            <span class="sub">دينار / ساعة</span>
                        </div>
                    </div>
                    <p class="live-sim-analysis" id="liveSimAnalysis" style="display:none;"></p>
                </div>
                <div class="tool-section" id="liveSimChartSection" style="display:none;">
                    <div class="live-sim-chart-grid">
                        <div class="live-sim-chart-wrapper">
                            <h4 style="margin:0 0 8px; font-size:16px; color:#fff;">منحنى الأداء اللحظي</h4>
                            <canvas id="liveSimChart" class="live-sim-chart-canvas" width="960" height="280"></canvas>
                            <div class="live-sim-legend">
                                <span><i style="background:#4ade80;"></i>الإنتاج الفعلي</span>
                                <span><i style="background:#60a5fa;"></i>التوقع اللحظي</span>
                                <span><i style="background:#f97316;"></i>الإنتاج المثالي</span>
                            </div>
                        </div>
                        <div class="live-sim-chart-wrapper">
                            <h4 style="margin:0 0 8px; font-size:16px; color:#fff;">منحنى التوقع اليومي</h4>
                            <canvas id="liveSimForecastChart" class="live-sim-chart-canvas" width="960" height="280"></canvas>
                            <div class="live-sim-legend">
                                <span><i style="background:#38bdf8;"></i>القدرة المتوقعة</span>
                            </div>
                        </div>
                        <div class="live-sim-chart-wrapper">
                            <h4 style="margin:0 0 8px; font-size:16px; color:#fff;">تغير الظروف الجوية</h4>
                            <canvas id="liveSimWeatherChart" class="live-sim-chart-canvas" width="960" height="280"></canvas>
                            <div class="live-sim-legend">
                                <span><i style="background:#fde047;"></i>مؤشر الأشعة فوق البنفسجية</span>
                                <span><i style="background:#a855f7;"></i>الغطاء السحابي %</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tool-section" id="liveSimWeatherSection" style="display:none;">
                    <h3>الظروف الجوية الحالية</h3>
                    <div class="live-sim-weather-grid">
                        <div class="live-sim-card"><span class="label">درجة الحرارة</span><strong id="liveSimWeatherTemp">-</strong><span class="sub">°C</span></div>
                        <div class="live-sim-card"><span class="label">الغطاء السحابي</span><strong id="liveSimWeatherCloud">-</strong><span class="sub">%</span></div>
                        <div class="live-sim-card"><span class="label">مؤشر الأشعة فوق البنفسجية</span><strong id="liveSimWeatherUv">-</strong></div>
                    </div>
                </div>
                <div class="tool-section" id="liveSimForecastSection" style="display:none;">
                    <h3>توقع الإنتاج لليوم</h3>
                    <div class="live-sim-grid-2">
                        <div class="live-sim-card"><span class="label">إجمالي الإنتاج المتوقع</span><strong id="liveSimForecastEnergy">-</strong><span class="sub">كيلوواط ساعة</span></div>
                        <div class="live-sim-card"><span class="label">العائد المقدر</span><strong id="liveSimForecastRevenue">-</strong><span class="sub">دينار أردني</span></div>
                    </div>
                    <div class="live-sim-chart-wrapper" style="margin-top:16px;">
                        <table class="live-sim-forecast-table" id="liveSimForecastTable">
                            <thead><tr><th>الوقت</th><th>القدرة المتوقعة (واط)</th></tr></thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            `;

            const form = container.querySelector('#liveSimulationForm');
            const startButton = form.querySelector('.live-sim-submit');
            const statusBox = container.querySelector('#liveSimStatus');
            const metricsSection = container.querySelector('#liveSimMetricsSection');
            const chartSection = container.querySelector('#liveSimChartSection');
            const weatherSection = container.querySelector('#liveSimWeatherSection');
            const forecastSection = container.querySelector('#liveSimForecastSection');
            const analysisParagraph = container.querySelector('#liveSimAnalysis');
            const mapStatus = container.querySelector('#liveSimMapStatus');
            const mapUseLocationBtn = container.querySelector('#liveSimUseLocation');
            const mapResetBtn = container.querySelector('#liveSimResetLocation');

            const livePowerEl = container.querySelector('#liveSimLivePower');
            const forecastPowerEl = container.querySelector('#liveSimForecastPower');
            const idealPowerEl = container.querySelector('#liveSimIdealPower');
            const performanceEl = container.querySelector('#liveSimPerformancePercent');
            const savingsEl = container.querySelector('#liveSimSavings');
            const weatherTempEl = container.querySelector('#liveSimWeatherTemp');
            const weatherCloudEl = container.querySelector('#liveSimWeatherCloud');
            const weatherUvEl = container.querySelector('#liveSimWeatherUv');
            const forecastEnergyEl = container.querySelector('#liveSimForecastEnergy');
            const forecastRevenueEl = container.querySelector('#liveSimForecastRevenue');
            const forecastTableBody = container.querySelector('#liveSimForecastTable tbody');

            const liveCanvas = container.querySelector('#liveSimChart');
            const liveCtx = liveCanvas?.getContext('2d');
            const forecastCanvas = container.querySelector('#liveSimForecastChart');
            const forecastCtx = forecastCanvas?.getContext('2d');
            const weatherCanvas = container.querySelector('#liveSimWeatherChart');
            const weatherCtx = weatherCanvas?.getContext('2d');

            const formInputs = form.querySelectorAll('input');
            const latInput = form.querySelector('input[name="latitude"]');
            const lngInput = form.querySelector('input[name="longitude"]');
            const defaultLocation = {
                lat: parseNumber(latInput?.value, 31.9539),
                lng: parseNumber(lngInput?.value, 35.9106),
            };
            let simulationTimer = null;
            let liveDataPoints = [];
            let forecastChartData = [];
            let weatherChartData = [];
            let currentValues = null;
            let dailyForecast = null;
            let weatherDataCache = null;
            let weatherFetchedAt = 0;
            let liveMap = null;
            let liveMarker = null;

            function setFormDisabled(disabled) {
                formInputs.forEach(input => {
                    input.disabled = disabled;
                });
                startButton.disabled = false;
            }

            function updateStatus(message, variant = 'info') {
                statusBox.style.display = 'flex';
                statusBox.style.background = variant === 'error'
                    ? 'rgba(255,77,77,0.12)'
                    : 'rgba(255,255,255,0.06)';
                statusBox.style.borderColor = variant === 'error'
                    ? 'rgba(255,77,77,0.35)'
                    : 'rgba(255,255,255,0.1)';
                statusBox.innerHTML = `<span class="tool-note-icon">${variant === 'error' ? '⚠️' : 'ℹ️'}</span><div>${message}</div>`;
            }

            function clearStatus() {
                statusBox.style.display = 'none';
            }

            function estimateIrradiance(uvIndex, cloudCover) {
                const uvBasedIrradiance = uvIndex * 100;
                const cloudFactor = 1 - (cloudCover * 0.75 / 100);
                return Math.max(0, uvBasedIrradiance * cloudFactor);
            }

            function calculatePowerOutput(systemSizeKw, irradiance, temperature, systemLossPercentage = 15) {
                const systemSizeWatts = systemSizeKw * 1000;
                const temperatureCoefficient = -0.0035;
                const systemLossFactor = 1 - (systemLossPercentage / 100);
                const tempDerating = 1 + ((temperature - 25) * temperatureCoefficient);
                const irradianceFactor = irradiance / 1000;
                const powerOutputWatts = systemSizeWatts * irradianceFactor * tempDerating * systemLossFactor;
                return powerOutputWatts > 0 ? powerOutputWatts : 0;
            }

            async function ensureWeatherData(lat, lng, force = false) {
                try {
                    const now = Date.now();
                    if (!weatherDataCache || force || now - weatherFetchedAt > 10 * 60 * 1000) {
                        weatherDataCache = await fetchWeatherData(lat, lng);
                        weatherFetchedAt = now;
                        if (currentValues && weatherDataCache) {
                            dailyForecast = buildDailyForecastFromApi(weatherDataCache.forecast, currentValues);
                            renderDailyForecast(dailyForecast);
                        }
                        if (weatherDataCache && weatherDataCache.forecast) {
                            weatherChartData = weatherDataCache.forecast.map(point => ({
                                time: new Date(point.time).toLocaleTimeString('ar-JO', { hour: '2-digit' }),
                                uv: Math.min(100, Math.max(0, point.uvIndex * 12.5)),
                                cloud: Math.min(100, Math.max(0, point.cloudCover)),
                            }));
                            drawWeatherChart();
                        }
                    }
                    return weatherDataCache;
                } catch (error) {
                    console.error('Error in ensureWeatherData:', error);
                    throw error; // Re-throw to be handled by calling function
                }
            }

            function buildPerformanceMessage(live, ideal, forecast, cloud) {
                if (ideal <= 0) {
                    return 'لا توجد أشعة شمس حالياً؛ سيتم استئناف الإنتاج عند شروق الشمس.';
                }
                const efficiency = (live / ideal) * 100;
                if (cloud > 70) {
                    return `الأداء الحالي منخفض كما هو متوقع بسبب غطاء سحابي نسبته ${Math.round(cloud)}%.`;
                }
                if (efficiency >= 85) {
                    return 'أداء ممتاز، النظام يعمل بكفاءة قريبة جداً من الظروف المثالية.';
                }
                if (efficiency >= 60) {
                    return 'الأداء جيد ومستقر، لكنه أقل بقليل من الظروف المثالية نتيجة درجة الحرارة والظروف الجوية الحالية.';
                }
                if (cloud < 40) {
                    return 'الأداء أقل من المتوقع في أجواء شبه صافية؛ يُنصح بالتحقق من نظافة الألواح أو التأكد من عدم وجود تظليل.';
                }
                return 'الأداء متوازن مع الظروف الحالية، وسيعود للتحسن مع تغير الطقس خلال اليوم.';
            }

            async function fetchWeatherData(lat, lng) {
                if (!WEATHER_API_KEY) {
                    throw new Error('لم يتم ضبط مفتاح WeatherAPI.');
                }
                const url = `https://api.weatherapi.com/v1/forecast.json?key=${WEATHER_API_KEY}&q=${lat},${lng}&days=1&aqi=no&alerts=no`;
                const response = await fetch(url, { cache: 'no-cache' });
                if (!response.ok) {
                    throw new Error('تعذر جلب بيانات الطقس من WeatherAPI.com');
                }
                const data = await response.json();
                if (!data?.current || !data?.forecast?.forecastday?.[0]?.hour) {
                    throw new Error('استجابة الطقس غير مكتملة.');
                }
                return {
                    current: {
                        temperature: parseFloat((data.current.temp_c ?? 0).toFixed(1)),
                        cloudCover: parseFloat((data.current.cloud ?? 0).toFixed(1)),
                        uvIndex: parseFloat((data.current.uv ?? 0).toFixed(1)),
                    },
                    forecast: data.forecast.forecastday[0].hour.map((hour) => ({
                        time: hour.time,
                        temperature: parseFloat((hour.temp_c ?? 0).toFixed(1)),
                        cloudCover: parseFloat((hour.cloud ?? 0).toFixed(1)),
                        uvIndex: parseFloat((hour.uv ?? 0).toFixed(1)),
                    })),
                };
            }

            function buildDailyForecastFromApi(forecast, values) {
                if (!forecast?.length) {
                    return null;
                }
                const chartData = forecast.map(point => {
                    const irradiance = estimateIrradiance(point.uvIndex, point.cloudCover);
                    const power = calculatePowerOutput(values.systemSize, irradiance, point.temperature);
                    const timeLabel = new Date(point.time).toLocaleTimeString('ar-JO', { hour: '2-digit', minute: '2-digit' });
                    return { time: timeLabel, power: parseFloat(power.toFixed(0)) };
                });
                const totalProductionKwh = chartData.reduce((total, item) => total + item.power / 1000, 0);
                return {
                    totalProductionKwh,
                    totalRevenue: totalProductionKwh * values.kwhPrice,
                    chartData,
                };
            }

            function renderDailyForecast(forecast) {
                if (!forecast) {
                    forecastSection.style.display = 'none';
                    forecastChartData = [];
                    drawForecastChart();
                    return;
                }
                forecastSection.style.display = 'block';
                forecastChartData = forecast.chartData || [];
                forecastEnergyEl.textContent = formatNumber(forecast.totalProductionKwh, 2);
                forecastRevenueEl.textContent = formatNumber(forecast.totalRevenue, 2);
                forecastTableBody.innerHTML = forecast.chartData
                    .slice(0, 12)
                    .map(row => `<tr><td>${row.time}</td><td>${formatNumber(row.power, 0)}</td></tr>`)
                    .join('');
                drawForecastChart();
            }

            function updateWeather(weather) {
                weatherTempEl.textContent = formatNumber(weather.temperature, 1);
                weatherCloudEl.textContent = `${formatNumber(weather.cloud, 0)}%`;
                weatherUvEl.textContent = formatNumber(weather.uv, 1);
            }

            function updateLatLngInputs(lat, lng, notify = true) {
                if (latInput) latInput.value = lat.toFixed(4);
                if (lngInput) lngInput.value = lng.toFixed(4);
                if (notify && mapStatus) {
                    mapStatus.textContent = `إحداثيات الموقع: ${lat.toFixed(4)}, ${lng.toFixed(4)}`;
                }
            }

            function ensureMap() {
                if (!container.isConnected) return;
                if (!window.L) {
                    if (mapStatus) mapStatus.textContent = 'تعذر تحميل خريطة Leaflet. تأكد من الاتصال بالإنترنت.';
                    return;
                }
                if (liveMap) return;
                liveMap = L.map('liveSimMap', { zoomControl: true, attributionControl: false }).setView([defaultLocation.lat, defaultLocation.lng], 11);
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    maxZoom: 19,
                }).addTo(liveMap);
                liveMarker = L.marker([defaultLocation.lat, defaultLocation.lng], { draggable: true }).addTo(liveMap);
                liveMarker.on('dragend', (event) => {
                    const point = event.target.getLatLng();
                    updateLatLngInputs(point.lat, point.lng);
                });
                liveMap.on('click', (event) => {
                    const { lat, lng } = event.latlng;
                    if (liveMarker) liveMarker.setLatLng([lat, lng]);
                    updateLatLngInputs(lat, lng);
                });
                updateLatLngInputs(defaultLocation.lat, defaultLocation.lng, false);
                if (mapStatus) mapStatus.textContent = 'انقر على الخريطة أو حرّك العلامة لتحديث الإحداثيات.';
            }

            function setMapLocation(lat, lng) {
                if (!window.L) return;
                ensureMap();
                if (liveMap) liveMap.setView([lat, lng], liveMap.getZoom() || 11);
                if (liveMarker) liveMarker.setLatLng([lat, lng]);
                updateLatLngInputs(lat, lng);
            }

            if (mapUseLocationBtn) {
                mapUseLocationBtn.addEventListener('click', () => {
                    if (!navigator.geolocation) {
                        if (mapStatus) mapStatus.textContent = 'المتصفح لا يدعم تحديد الموقع الجغرافي.';
                        return;
                    }
                    mapStatus.textContent = 'جاري تحديد موقعك...';
                    navigator.geolocation.getCurrentPosition((position) => {
                        const { latitude, longitude } = position.coords;
                        setMapLocation(latitude, longitude);
                        mapStatus.textContent = 'تم تحديث الإحداثيات حسب موقعك الحالي.';
                    }, () => {
                        mapStatus.textContent = 'تعذر الحصول على موقعك الحالي.';
                    }, {
                        enableHighAccuracy: true,
                        timeout: 8000,
                    });
                });
            }

            if (mapResetBtn) {
                mapResetBtn.addEventListener('click', () => {
                    setMapLocation(defaultLocation.lat, defaultLocation.lng);
                    mapStatus.textContent = 'تمت إعادة تعيين الموقع إلى الإحداثيات الافتراضية.';
                });
            }

            [latInput, lngInput].forEach(input => {
                if (!input) return;
                input.addEventListener('change', () => {
                    const lat = parseNumber(latInput.value, defaultLocation.lat);
                    const lng = parseNumber(lngInput.value, defaultLocation.lng);
                    setMapLocation(lat, lng);
                });
            });

            function updateMetrics(point, analysis) {
                metricsSection.style.display = 'block';
                chartSection.style.display = 'block';
                weatherSection.style.display = 'block';
                analysisParagraph.style.display = 'block';

                livePowerEl.textContent = formatNumber(point.livePower, 0);
                forecastPowerEl.textContent = formatNumber(point.forecastPower, 0);
                idealPowerEl.textContent = formatNumber(point.idealPower, 0);
                performanceEl.textContent = `${formatNumber(point.efficiency, 1)}%`;
                savingsEl.textContent = `${formatNumber(point.savings, 3)} دينار/ساعة`;
                analysisParagraph.textContent = analysis;
                updateWeather(point.weather);
            }

            function drawLiveChart() {
                if (!liveCtx || !liveCanvas) return;
                const ratio = window.devicePixelRatio || 1;
                const width = liveCanvas.width / ratio;
                const height = liveCanvas.height / ratio;
                liveCtx.clearRect(0, 0, width, height);

                if (!liveDataPoints.length) {
                    liveCtx.fillStyle = 'rgba(255,255,255,0.6)';
                    liveCtx.font = '14px sans-serif';
                    liveCtx.fillText('ابدأ المحاكاة لعرض بيانات الإنتاج اللحظية.', 20, 40);
                    return;
                }

                const padding = 40;
                const innerWidth = width - padding * 2;
                const innerHeight = height - padding * 2;
                const maxValue = Math.max(10, ...liveDataPoints.map(p => Math.max(p.livePower, p.forecastPower, p.idealPower)));

                liveCtx.strokeStyle = 'rgba(255,255,255,0.2)';
                liveCtx.lineWidth = 1;
                liveCtx.beginPath();
                liveCtx.moveTo(padding, padding - 10);
                liveCtx.lineTo(padding, height - padding);
                liveCtx.lineTo(width - padding + 10, height - padding);
                liveCtx.stroke();

                liveCtx.fillStyle = 'rgba(255,255,255,0.45)';
                liveCtx.font = '11px sans-serif';
                liveCtx.textAlign = 'right';
                liveCtx.fillText(`${Math.round(maxValue)} W`, padding - 12, padding + 4);
                liveCtx.fillText('0', padding - 12, height - padding + 4);

                liveCtx.textAlign = 'center';
                const labelEvery = Math.max(1, Math.floor(liveDataPoints.length / 6));
                liveDataPoints.forEach((point, index) => {
                    if (index % labelEvery === 0 || index === liveDataPoints.length - 1) {
                        const x = padding + (innerWidth * index) / Math.max(liveDataPoints.length - 1, 1);
                        liveCtx.fillText(point.timeLabel, x, height - padding + 18);
                    }
                });

                function plotLine(key, color) {
                    liveCtx.beginPath();
                    liveDataPoints.forEach((point, index) => {
                        const x = padding + (innerWidth * index) / Math.max(liveDataPoints.length - 1, 1);
                        const value = point[key];
                        const y = height - padding - (value / maxValue) * innerHeight;
                        if (index === 0) liveCtx.moveTo(x, y);
                        else liveCtx.lineTo(x, y);
                    });
                    liveCtx.strokeStyle = color;
                    liveCtx.lineWidth = 2;
                    liveCtx.stroke();
                }

                plotLine('idealPower', '#f97316');
                plotLine('forecastPower', '#60a5fa');
                plotLine('livePower', '#4ade80');
            }

            function drawForecastChart() {
                if (!forecastCtx || !forecastCanvas) return;
                const ratio = window.devicePixelRatio || 1;
                const width = forecastCanvas.width / ratio;
                const height = forecastCanvas.height / ratio;
                forecastCtx.clearRect(0, 0, width, height);

                if (!forecastChartData.length) {
                    forecastCtx.fillStyle = 'rgba(255,255,255,0.6)';
                    forecastCtx.font = '14px sans-serif';
                    forecastCtx.fillText('لا توجد بيانات توقع بعد.', 20, 40);
                    return;
                }

                const padding = 40;
                const innerWidth = width - padding * 2;
                const innerHeight = height - padding * 2;
                const maxValue = Math.max(10, ...forecastChartData.map(p => p.power));

                forecastCtx.strokeStyle = 'rgba(255,255,255,0.2)';
                forecastCtx.lineWidth = 1;
                forecastCtx.beginPath();
                forecastCtx.moveTo(padding, padding - 10);
                forecastCtx.lineTo(padding, height - padding);
                forecastCtx.lineTo(width - padding + 10, height - padding);
                forecastCtx.stroke();

                forecastCtx.fillStyle = 'rgba(255,255,255,0.45)';
                forecastCtx.font = '11px sans-serif';
                forecastCtx.textAlign = 'right';
                forecastCtx.fillText(`${Math.round(maxValue)} W`, padding - 12, padding + 4);
                forecastCtx.fillText('0', padding - 12, height - padding + 4);

                forecastCtx.textAlign = 'center';
                const labelEvery = Math.max(1, Math.floor(forecastChartData.length / 6));
                forecastChartData.forEach((point, index) => {
                    if (index % labelEvery === 0 || index === forecastChartData.length - 1) {
                        const x = padding + (innerWidth * index) / Math.max(forecastChartData.length - 1, 1);
                        forecastCtx.fillText(point.time, x, height - padding + 18);
                    }
                });

                forecastCtx.beginPath();
                forecastChartData.forEach((point, index) => {
                    const x = padding + (innerWidth * index) / Math.max(forecastChartData.length - 1, 1);
                    const y = height - padding - (point.power / maxValue) * innerHeight;
                    if (index === 0) forecastCtx.moveTo(x, y);
                    else forecastCtx.lineTo(x, y);
                });
                forecastCtx.strokeStyle = '#38bdf8';
                forecastCtx.lineWidth = 2;
                forecastCtx.stroke();
            }

            function drawWeatherChart() {
                if (!weatherCtx || !weatherCanvas) return;
                const ratio = window.devicePixelRatio || 1;
                const width = weatherCanvas.width / ratio;
                const height = weatherCanvas.height / ratio;
                weatherCtx.clearRect(0, 0, width, height);

                if (!weatherChartData.length) {
                    weatherCtx.fillStyle = 'rgba(255,255,255,0.6)';
                    weatherCtx.font = '14px sans-serif';
                    weatherCtx.fillText('لا توجد بيانات طقس متاحة.', 20, 40);
                    return;
                }

                const padding = 40;
                const innerWidth = width - padding * 2;
                const innerHeight = height - padding * 2;

                weatherCtx.strokeStyle = 'rgba(255,255,255,0.2)';
                weatherCtx.lineWidth = 1;
                weatherCtx.beginPath();
                weatherCtx.moveTo(padding, padding - 10);
                weatherCtx.lineTo(padding, height - padding);
                weatherCtx.lineTo(width - padding + 10, height - padding);
                weatherCtx.stroke();

                weatherCtx.fillStyle = 'rgba(255,255,255,0.45)';
                weatherCtx.font = '11px sans-serif';
                weatherCtx.textAlign = 'right';
                weatherCtx.fillText('100', padding - 12, padding + 4);
                weatherCtx.fillText('0', padding - 12, height - padding + 4);

                weatherCtx.textAlign = 'center';
                const labelEvery = Math.max(1, Math.floor(weatherChartData.length / 6));
                weatherChartData.forEach((point, index) => {
                    if (index % labelEvery === 0 || index === weatherChartData.length - 1) {
                        const x = padding + (innerWidth * index) / Math.max(weatherChartData.length - 1, 1);
                        weatherCtx.fillText(point.time, x, height - padding + 18);
                    }
                });

                function plotWeatherLine(key, color) {
                    weatherCtx.beginPath();
                    weatherChartData.forEach((point, index) => {
                        const x = padding + (innerWidth * index) / Math.max(weatherChartData.length - 1, 1);
                        const y = height - padding - (point[key] / 100) * innerHeight;
                        if (index === 0) weatherCtx.moveTo(x, y);
                        else weatherCtx.lineTo(x, y);
                    });
                    weatherCtx.strokeStyle = color;
                    weatherCtx.lineWidth = 2;
                    weatherCtx.stroke();
                }

                plotWeatherLine('uv', '#fde047');
                plotWeatherLine('cloud', '#a855f7');
            }

            function resizeCanvases() {
                const wrappers = chartSection.querySelectorAll('.live-sim-chart-wrapper');
                const ratio = window.devicePixelRatio || 1;
                wrappers.forEach((wrapper) => {
                    const rect = wrapper.getBoundingClientRect();
                    const width = Math.max(320, rect.width - 32);
                    if (wrapper.contains(liveCanvas)) {
                        liveCanvas.width = width * ratio;
                        liveCanvas.height = 280 * ratio;
                        liveCanvas.style.width = `${width}px`;
                        liveCanvas.style.height = '280px';
                        liveCtx?.setTransform(ratio, 0, 0, ratio, 0, 0);
                    } else if (wrapper.contains(forecastCanvas)) {
                        forecastCanvas.width = width * ratio;
                        forecastCanvas.height = 280 * ratio;
                        forecastCanvas.style.width = `${width}px`;
                        forecastCanvas.style.height = '280px';
                        forecastCtx?.setTransform(ratio, 0, 0, ratio, 0, 0);
                    } else if (wrapper.contains(weatherCanvas)) {
                        weatherCanvas.width = width * ratio;
                        weatherCanvas.height = 280 * ratio;
                        weatherCanvas.style.width = `${width}px`;
                        weatherCanvas.style.height = '280px';
                        weatherCtx?.setTransform(ratio, 0, 0, ratio, 0, 0);
                    }
                });
                drawLiveChart();
                drawForecastChart();
                drawWeatherChart();
            }

            function getFormValues() {
                const formData = new FormData(form);
                return {
                    systemSize: parseNumber(formData.get('systemSize'), 0),
                    kwhPrice: parseNumber(formData.get('kwhPrice'), 0),
                    panelTilt: parseNumber(formData.get('panelTilt'), 0),
                    panelAzimuth: parseNumber(formData.get('panelAzimuth'), 0),
                    latitude: parseNumber(formData.get('latitude'), 0),
                    longitude: parseNumber(formData.get('longitude'), 0),
                };
            }

            function validateValues(values) {
                const errors = [];
                if (values.systemSize <= 0) errors.push('حجم النظام يجب أن يكون أكبر من صفر.');
                if (values.kwhPrice <= 0) errors.push('سعر الكيلوواط/ساعة يجب أن يكون أكبر من صفر.');
                if (values.panelTilt < 0 || values.panelTilt > 90) errors.push('زاوية الميل يجب أن تكون بين 0 و 90 درجة.');
                if (values.panelAzimuth < 0 || values.panelAzimuth > 360) errors.push('زاوية الاتجاه يجب أن تكون بين 0 و 360 درجة.');
                return errors;
            }

            function stopSimulation(silent = false) {
                if (simulationTimer) {
                    clearInterval(simulationTimer);
                    simulationTimer = null;
                }
                currentValues = null;
                startButton.dataset.mode = 'start';
                startButton.textContent = 'بدء المحاكاة والتحليل';
                setFormDisabled(false);
                if (silent) {
                    clearStatus();
                } else {
                    updateStatus('تم إيقاف المحاكاة. يمكن تعديل المدخلات لإعادة التشغيل.', 'info');
                }
            }

            async function runSimulationStep(forceFetch = false) {
                if (!currentValues) return;
                try {
                    const weatherData = await ensureWeatherData(currentValues.latitude, currentValues.longitude, forceFetch);
                    if (!weatherData) {
                        throw new Error('بيانات الطقس غير متاحة');
                    }

                    const now = new Date();
                    const current = weatherData.current;
                    const forecastPoint = (() => {
                        const targetHour = now.getHours();
                        return weatherData.forecast.find(point => {
                            const hour = new Date(point.time).getHours();
                            return hour === targetHour;
                        }) || weatherData.forecast[0];
                    })();

                    const liveIrradiance = estimateIrradiance(current.uvIndex, current.cloudCover);
                    const forecastIrradiance = estimateIrradiance(forecastPoint.uvIndex, forecastPoint.cloudCover);
                    const livePower = calculatePowerOutput(currentValues.systemSize, liveIrradiance, current.temperature);
                    const forecastPower = calculatePowerOutput(currentValues.systemSize, forecastIrradiance, forecastPoint.temperature);
                    const idealPower = calculatePowerOutput(currentValues.systemSize, 1000, 25, 0);
                    const savings = (livePower / 1000) * currentValues.kwhPrice;
                    const efficiency = idealPower > 0 ? (livePower / idealPower) * 100 : 0;
                    const timeLabel = now.toLocaleTimeString('ar-JO', { hour: '2-digit', minute: '2-digit' });
                    const analysis = buildPerformanceMessage(livePower, idealPower, forecastPower, current.cloudCover);

                    const point = {
                        timeLabel,
                        livePower,
                        forecastPower,
                        idealPower,
                        savings,
                        efficiency,
                        weather: {
                            temperature: current.temperature,
                            cloud: current.cloudCover,
                            uv: current.uvIndex,
                        },
                    };

                    liveDataPoints.push(point);
                    if (liveDataPoints.length > 30) {
                        liveDataPoints.shift();
                    }

                    updateMetrics(point, analysis);
                    drawLiveChart();
                    updateStatus(`آخر تحديث عند ${timeLabel}`, 'info');
                } catch (error) {
                    console.error('Live simulation update failed', error);
                    updateStatus('تعذر تحديث بيانات الطقس. يرجى التحقق من الاتصال أو مفتاح WeatherAPI.', 'error');
                    stopSimulation(true);
                }
            }

            async function startSimulation(values) {
                setFormDisabled(true);
                startButton.dataset.mode = 'stop';
                startButton.textContent = 'إيقاف المحاكاة';
                updateStatus('جاري جلب بيانات الطقس من WeatherAPI...', 'info');

                try {
                    weatherDataCache = await fetchWeatherData(values.latitude, values.longitude);
                    weatherFetchedAt = Date.now();
                } catch (error) {
                    console.error('Weather fetch failed', error);
                    updateStatus('تعذر جلب بيانات الطقس. تأكد من صحة المفتاح والاتصال بالإنترنت.', 'error');
                    startButton.dataset.mode = 'start';
                    startButton.textContent = 'بدء المحاكاة والتحليل';
                    setFormDisabled(false);
                    return;
                }

                currentValues = values;
                liveDataPoints = [];
                dailyForecast = buildDailyForecastFromApi(weatherDataCache.forecast, values);
                forecastChartData = dailyForecast?.chartData || [];
                weatherChartData = (weatherDataCache.forecast || []).map(point => ({
                    time: new Date(point.time).toLocaleTimeString('ar-JO', { hour: '2-digit' }),
                    uv: Math.min(100, Math.max(0, point.uvIndex * 12.5)),
                    cloud: Math.min(100, Math.max(0, point.cloudCover)),
                }));
                renderDailyForecast(dailyForecast);
                drawForecastChart();
                drawWeatherChart();
                updateWeather({
                    temperature: weatherDataCache.current.temperature,
                    cloud: weatherDataCache.current.cloudCover,
                    uv: weatherDataCache.current.uvIndex,
                });

                clearStatus();
                updateStatus('المحاكاة قيد التشغيل... سيتم تحديث القراءات كل دقيقة.', 'info');

                resizeCanvases();
                await runSimulationStep(true);
                simulationTimer = setInterval(() => runSimulationStep(false), 60000);
            }

            form.addEventListener('submit', async (event) => {
                event.preventDefault();
                if (startButton.dataset.mode === 'stop') {
                    stopSimulation();
                    return;
                }

                const values = getFormValues();
                const errors = validateValues(values);
                if (errors.length) {
                    updateStatus(errors.join('<br>'), 'error');
                    return;
                }

                await startSimulation(values);
            });

            const resizeHandler = () => resizeCanvases();
            window.addEventListener('resize', resizeHandler);
            resizeCanvases();
            ensureMap();

            return () => {
                stopSimulation(true);
                window.removeEventListener('resize', resizeHandler);
                if (liveMap) {
                    liveMap.remove();
                    liveMap = null;
                    liveMarker = null;
                }
            };
        }

        function renderToolDesignOptimizer(container) {
            if (!container) {
                return;
            }

            container.innerHTML = `
                <div class="tool-section">
                    <h3>حاسبة حجم النظام الفني والمالي</h3>
                    <p>أدخل بيانات الاستهلاك والمساحة والتكاليف لتحصل على نظام مقترح وتحليل مالي مبسط.</p>
                    <form id="designOptimizerForm" class="tool-grid cols-3">
                        <div class="tool-input-group">
                            <label>نمط الحساب</label>
                            <select name="calculationMode">
                                <option value="consumption" selected>حسب الاستهلاك الشهري</option>
                                <option value="bill">حسب قيمة الفاتورة</option>
                            </select>
                        </div>
                        <div class="tool-input-group" data-mode="consumption">
                            <label>الاستهلاك الشهري (ك.و س)</label>
                            <input type="number" name="monthlyConsumption" value="700" step="10" min="50" required>
                        </div>
                        <div class="tool-input-group" data-mode="bill" style="display:none;">
                            <label>قيمة الفاتورة الشهرية (دينار)</label>
                            <input type="number" name="monthlyBill" value="85" step="5" min="10">
                        </div>
                        <div class="tool-input-group">
                            <label>سعر الكيلوواط ساعة (دينار)</label>
                            <input type="number" name="kwhPrice" value="0.12" step="0.01" min="0.05" required>
                        </div>
                        <div class="tool-input-group">
                            <label>المساحة المتاحة (م²)</label>
                            <input type="number" name="surfaceArea" value="80" min="10" step="1" required>
                        </div>
                        <div class="tool-input-group">
                            <label>قدرة اللوح (واط)</label>
                            <input type="number" name="panelWattage" value="550" step="10" min="200" required>
                        </div>
                        <div class="tool-input-group">
                            <label>الخسائر الكلية للنظام (%)</label>
                            <input type="number" name="systemLoss" value="15" step="1" min="0" max="40" required>
                        </div>
                        <div class="tool-input-group">
                            <label>تكلفة التركيب لكل ك.و (دينار)</label>
                            <input type="number" name="costPerKw" value="600" step="10" min="200" required>
                        </div>
                        <div class="tool-input-group">
                            <label>الموقع</label>
                            <select name="location">
                                <option value="amman" selected>عمان</option>
                                <option value="zarqa">الزرقاء</option>
                                <option value="irbid">إربد</option>
                                <option value="aqaba">العقبة</option>
                            </select>
                        </div>
                        <div class="tool-input-group">
                            <label>تكاليف التشغيل السنوية (%)</label>
                            <input type="number" name="omPercent" value="2" step="0.1" min="0" max="10" required>
                        </div>
                        <div class="tool-input-group">
                            <label>معدل التهالك السنوي (%)</label>
                            <input type="number" name="degradationRate" value="0.7" step="0.1" min="0" max="5" required>
                        </div>
                        <div class="tool-input-group">
                            <label>عمر المشروع (سنوات)</label>
                            <input type="number" name="projectLife" value="20" step="1" min="5" max="30" required>
                        </div>
                        <div class="tool-input-group">
                            <label>معدل تضخم التعرفة السنوي (%)</label>
                            <input type="number" name="inflation" value="2" step="0.1" min="0" max="10" required>
                        </div>
                        <button type="submit" class="tool-action-btn" style="grid-column: span 3;">ابدأ التحليل</button>
                    </form>
                </div>
                <div class="tool-notice" id="designOptimizerAlert" style="display:none;"></div>
                <div class="tool-results-card" id="designOptimizerResults" style="display:none;"></div>
            `;

            const form = container.querySelector('#designOptimizerForm');
            const alertBox = container.querySelector('#designOptimizerAlert');
            const results = container.querySelector('#designOptimizerResults');
            const modeSelect = form.querySelector('select[name="calculationMode"]');
            const consumptionField = form.querySelector('[data-mode="consumption"]');
            const billFields = Array.from(form.querySelectorAll('[data-mode="bill"]'));
            const formatCurrency = (value) => `${formatNumber(value, 0)} دينار`;
            const formatKw = (value) => `${formatNumber(value, 2)} ك.و`;

            function toggleMode(mode) {
                if (consumptionField) {
                    consumptionField.style.display = mode === 'bill' ? 'none' : 'flex';
                }
                billFields.forEach(el => {
                    el.style.display = mode === 'bill' ? 'flex' : 'none';
                });
            }

            toggleMode(modeSelect.value);
            modeSelect.addEventListener('change', (event) => {
                toggleMode(event.target.value);
            });

            form.addEventListener('submit', (event) => {
                event.preventDefault();
                if (alertBox) {
                    alertBox.style.display = 'none';
                    alertBox.innerHTML = '';
                }

                const data = new FormData(form);
                const values = {
                    calculationMode: data.get('calculationMode') || 'consumption',
                    monthlyConsumption: parseNumber(data.get('monthlyConsumption'), 0),
                    monthlyBill: parseNumber(data.get('monthlyBill'), 0),
                    kwhPrice: parseNumber(data.get('kwhPrice'), 0.12),
                    surfaceArea: parseNumber(data.get('surfaceArea'), 0),
                    panelWattage: parseNumber(data.get('panelWattage'), 0),
                    systemLoss: parseNumber(data.get('systemLoss'), 15),
                    costPerKw: parseNumber(data.get('costPerKw'), 600),
                    location: (data.get('location') || 'amman').toString(),
                    omPercent: parseNumber(data.get('omPercent'), 2),
                    degradationRate: parseNumber(data.get('degradationRate'), 0.7),
                    projectLife: parseNumber(data.get('projectLife'), 20),
                    inflation: parseNumber(data.get('inflation'), 2),
                };

                const errors = [];
                if (values.calculationMode === 'consumption' && values.monthlyConsumption <= 0) {
                    errors.push('رجاءً أدخل الاستهلاك الشهري بالكيلوواط ساعة.');
                }
                if (values.calculationMode === 'bill') {
                    if (values.monthlyBill <= 0) errors.push('قيمة الفاتورة الشهرية يجب أن تكون أكبر من صفر.');
                    if (values.kwhPrice <= 0) errors.push('سعر الكيلوواط ساعة يجب أن يكون أكبر من صفر.');
                }
                if (values.surfaceArea <= 0) errors.push('المساحة المتاحة يجب أن تكون موجبة.');
                if (values.panelWattage <= 0) errors.push('قدرة اللوح يجب أن تكون موجبة.');
                if (values.kwhPrice <= 0) errors.push('سعر الكيلوواط ساعة يجب أن يكون موجبا.');
                if (values.costPerKw <= 0) errors.push('تكلفة التركيب لكل كيلوواط يجب أن تكون موجبة.');

                if (errors.length) {
                    if (alertBox) {
                        alertBox.style.display = 'flex';
                        alertBox.innerHTML = `<span class="tool-note-icon">⚠️</span><div>${errors.join('<br>')}</div>`;
                    }
                    if (results) {
                        results.style.display = 'none';
                        results.innerHTML = '';
                    }
                    return;
                }

                const calculation = calculateDesignOptimizationLocal(values);
                if (!calculation) {
                    if (alertBox) {
                        alertBox.style.display = 'flex';
                        alertBox.innerHTML = `<span class="tool-note-icon">⚠️</span><div>لم نتمكن من إيجاد حجم مناسب بهذه المدخلات. الرجاء التحقق من القيم وإعادة المحاولة.</div>`;
                    }
                    if (results) {
                        results.style.display = 'none';
                        results.innerHTML = '';
                    }
                    return;
                }

                const { panelConfig, inverterConfig, wiringConfig, financial, reasoning, limitingFactor, inputs } = calculation;
                const finalCumulative = financial.cashFlowAnalysis && financial.cashFlowAnalysis.length
                    ? financial.cashFlowAnalysis[financial.cashFlowAnalysis.length - 1].cumulative
                    : financial.netProfit;

                const monthlyRows = (financial.monthlyBreakdown || []).map(item => `
                    <tr>
                        <td>${item.month}</td>
                        <td>${formatNumber(item.sunHours, 2)}</td>
                        <td>${formatNumber(item.production, 1)}</td>
                        <td>${formatCurrency(item.revenue)}</td>
                    </tr>
                `).join('');

                const cashflowRows = (financial.cashFlowAnalysis || []).map(item => `
                    <tr>
                        <td>${item.year}</td>
                        <td>${formatNumber(item.production, 0)}</td>
                        <td>${formatCurrency(item.revenue)}</td>
                        <td>${formatCurrency(item.omCost)}</td>
                        <td>${formatCurrency(item.netCash)}</td>
                        <td>${formatCurrency(item.cumulative)}</td>
                    </tr>
                `).join('');

                const paybackText = financial.paybackPeriodYears
                    ? `${formatNumber(financial.paybackPeriodYears, 1)} سنة (${formatNumber(financial.paybackPeriodMonths || (financial.paybackPeriodYears * 12), 0)} شهر)`
                    : 'غير متحقق خلال الفترة المحددة';

                if (results) {
                    results.style.display = 'flex';
                    results.innerHTML = `
                        <div class="tool-section">
                            <h4>ملخص التصميم الفني</h4>
                            <div class="tool-results-grid cols-3">
                                <div class="tool-stat"><span>قدرة النظام DC</span><strong>${formatKw(panelConfig.totalDcPower)}</strong></div>
                                <div class="tool-stat"><span>عدد الألواح</span><strong>${panelConfig.panelCount} لوح</strong></div>
                                <div class="tool-stat"><span>المساحة المطلوبة</span><strong>${formatNumber(panelConfig.requiredArea, 1)} م²</strong></div>
                                <div class="tool-stat"><span>العامل المحدد</span><strong>${limitingFactor === 'area' ? 'المساحة المتاحة' : 'الاستهلاك'}</strong></div>
                                <div class="tool-stat"><span>قدرة العاكس المقترحة</span><strong>${inverterConfig.recommendedSize}</strong></div>
                                <div class="tool-stat"><span>طور العاكس</span><strong>${inverterConfig.phase}</strong></div>
                            </div>
                            <div class="tool-notice" style="margin-top:12px;">
                                <span class="tool-note-icon">📋</span>
                                <div>${reasoning}</div>
                            </div>
                        </div>

                        <div class="tool-section">
                            <h4>التحليل المالي</h4>
                            <div class="tool-results-grid cols-3">
                                <div class="tool-stat"><span>الاستثمار الكلي</span><strong>${formatCurrency(financial.totalInvestment)}</strong></div>
                                <div class="tool-stat"><span>الإيراد السنوي المتوقع</span><strong>${formatCurrency(financial.annualRevenue)}</strong></div>
                                <div class="tool-stat"><span>تكلفة التشغيل السنوية</span><strong>${formatCurrency(financial.annualOMCost)}</strong></div>
                                <div class="tool-stat"><span>الإنتاج السنوي</span><strong>${formatNumber(financial.totalAnnualProduction, 0)} ك.و س</strong></div>
                                <div class="tool-stat"><span>فترة الاسترداد</span><strong>${paybackText}</strong></div>
                                <div class="tool-stat"><span>صافي الربح بنهاية الفترة</span><strong>${formatCurrency(finalCumulative)}</strong></div>
                            </div>
                            <div class="tool-notice" style="margin-top:12px;">
                                <span class="tool-note-icon">ℹ️</span>
                                <div>تم افتراض معدل تضخم ${formatNumber(values.inflation,1)}% سنوياً للسعر وتهالك ${formatNumber(values.degradationRate,1)}% للإنتاج.</div>
                            </div>
                        </div>

                        <div class="tool-section">
                            <h4>التدفق النقدي (${values.projectLife} سنة)</h4>
                            <div class="tool-table-wrapper">
                                <table class="tool-table">
                                    <thead>
                                        <tr><th>السنة</th><th>الإنتاج (ك.و س)</th><th>الإيراد</th><th>التشغيل والصيانة</th><th>صافي التدفق</th><th>التدفق التراكمي</th></tr>
                                    </thead>
                                    <tbody>${cashflowRows}</tbody>
                                </table>
                            </div>
                        </div>

                        <div class="tool-section">
                            <h4>التوزيع الشهري للسنة الأولى</h4>
                            <div class="tool-table-wrapper">
                                <table class="tool-table">
                                    <thead>
                                        <tr><th>الشهر</th><th>متوسط ساعات الشمس</th><th>الإنتاج (ك.و س)</th><th>الإيراد</th></tr>
                                    </thead>
                                    <tbody>${monthlyRows}</tbody>
                                </table>
                            </div>
                        </div>

                        <div class="tool-section">
                            <div class="tool-report-actions" style="display:flex; gap:12px; flex-wrap:wrap;">
                                <button type="button" class="tool-action-btn" id="designAddToReport">أضف النتائج إلى التقرير</button>
                            </div>
                        </div>
                    `;

                    const addReportBtn = results.querySelector('#designAddToReport');
                    if (addReportBtn) {
                        addReportBtn.addEventListener('click', () => {
                            window.energyReport.addCard({
                                type: 'تصميم النظام الفني والمالي',
                                summary: `نظام ${formatKw(panelConfig.totalDcPower)} يغطي حوالي ${formatNumber(inputs.annualConsumption, 0)} ك.و س/سنة`,
                                values: {
                                    'عدد الألواح': `${panelConfig.panelCount} لوح`,
                                    'الاستثمار الكلي': formatCurrency(financial.totalInvestment),
                                    'الإيراد السنوي': formatCurrency(financial.annualRevenue),
                                    'فترة الاسترداد': paybackText,
                                }
                            });
                        });
                    }
                }
            });
        }
        function renderToolStringConfiguration(container) {
            container.innerHTML = `
                <div class="tool-section">
                    <h3>حاسبة تهيئة السلاسل</h3>
                    <p>أدخل مواصفات اللوح والعاكس للحصول على نطاق الألواح الآمن لكل سلسلة وعدد السلاسل المتوازية حسب القدرة المطلوبة.</p>
                    <form id="stringConfigForm" class="tool-grid cols-2">
                        <div class="tool-input-group"><label>Voc (فولت)</label><input type="number" name="voc" value="50.5" step="0.1" required></div>
                        <div class="tool-input-group"><label>Vmp (فولت)</label><input type="number" name="vmp" value="42.5" step="0.1" required></div>
                        <div class="tool-input-group"><label>Isc (أمبير)</label><input type="number" name="isc" value="13.5" step="0.1" required></div>
                        <div class="tool-input-group"><label>Imp (أمبير)</label><input type="number" name="imp" value="12.9" step="0.1"></div>
                        <div class="tool-input-group"><label>معامل الحرارة للجهد (% لكل درجة)</label><input type="number" name="tempCoefficient" value="-0.32" step="0.01" required></div>
                        <div class="tool-input-group"><label>قدرة اللوح (واط)</label><input type="number" name="panelWattage" value="550" step="10" required></div>
                        <div class="tool-input-group"><label>أدنى جهد MPPT</label><input type="number" name="mpptMin" value="200" step="5" required></div>
                        <div class="tool-input-group"><label>أقصى جهد MPPT</label><input type="number" name="mpptMax" value="800" step="5" required></div>
                        <div class="tool-input-group"><label>أقصى جهد للعاكس</label><input type="number" name="inverterMaxVolt" value="1000" step="5" required></div>
                        <div class="tool-input-group"><label>أقصى تيار للعاكس (أمبير)</label><input type="number" name="inverterMaxCurrent" value="30" step="0.5" required></div>
                        <div class="tool-input-group"><label>أدنى درجة حرارة متوقعة (°C)</label><input type="number" name="minTemp" value="-5" step="1" required></div>
                        <div class="tool-input-group"><label>أعلى درجة حرارة متوقعة (°C)</label><input type="number" name="maxTemp" value="65" step="1" required></div>
                        <div class="tool-input-group"><label>حجم النظام المستهدف (ك.و ذروة)</label><input type="number" name="targetSystemSize" value="10" step="0.1" required></div>
                        <button type="submit" class="tool-action-btn">احسب التكوين</button>
                    </form>
                    <div class="tool-notice" id="stringConfigAlert" style="display:none;"></div>
                </div>
                <div class="tool-results-card" id="stringConfigSummary" style="display:none;"></div>
                <div class="tool-results-card" id="stringConfigArray" style="display:none;"></div>
                <div class="tool-results-card" id="stringConfigReasoning" style="display:none;"></div>
            `;

            const form = container.querySelector('#stringConfigForm');
            const alertBox = container.querySelector('#stringConfigAlert');
            const summaryCard = container.querySelector('#stringConfigSummary');
            const arrayCard = container.querySelector('#stringConfigArray');
            const reasoningCard = container.querySelector('#stringConfigReasoning');

            const hideCards = () => {
                alertBox.style.display = 'none';
                summaryCard.style.display = 'none';
                arrayCard.style.display = 'none';
                reasoningCard.style.display = 'none';
            };

            form.addEventListener('submit', async (event) => {
                event.preventDefault();
                hideCards();
                const submitBtn = form.querySelector('button[type="submit"]');
                const restoreSubmit = () => {
                    if (submitBtn) {
                        submitBtn.disabled = false;
                        submitBtn.textContent = submitBtn.dataset.originalText || 'احسب التكوين';
                    }
                };
                if (submitBtn) {
                    submitBtn.dataset.originalText = submitBtn.textContent;
                    submitBtn.disabled = true;
                    submitBtn.textContent = 'جارٍ الحساب...';
                }

                const data = Object.fromEntries(new FormData(form).entries());
                const input = {
                    voc: parseNumber(data.voc, 0),
                    vmp: parseNumber(data.vmp, 0),
                    isc: parseNumber(data.isc, 0),
                    tempCoefficient: parseNumber(data.tempCoefficient, -0.32),
                    imp: parseNumber(data.imp, 0),
                    panelWattage: parseNumber(data.panelWattage, 550),
                    mpptMin: parseNumber(data.mpptMin, 0),
                    mpptMax: parseNumber(data.mpptMax, 0),
                    inverterMaxVolt: parseNumber(data.inverterMaxVolt, 0),
                    inverterMaxCurrent: parseNumber(data.inverterMaxCurrent, 0),
                    minTemp: parseNumber(data.minTemp, 0),
                    maxTemp: parseNumber(data.maxTemp, 0),
                    targetSystemSize: parseNumber(data.targetSystemSize, 1),
                };

                const errors = [];
                if (input.voc <= 0) errors.push('Voc يجب أن يكون أكبر من صفر.');
                if (input.vmp <= 0) errors.push('Vmp يجب أن يكون أكبر من صفر.');
                if (input.isc <= 0) errors.push('Isc يجب أن يكون أكبر من صفر.');
                if (input.panelWattage <= 0) errors.push('قدرة اللوح يجب أن تكون قيمة موجبة.');
                if (input.imp !== 0 && input.imp < 0) errors.push('Imp يجب أن يكون قيمة موجبة إذا تم إدخاله.');
                if (input.mpptMin <= 0 || input.mpptMax <= 0 || input.mpptMin >= input.mpptMax) errors.push('تأكد أن نطاق MPPT صحيح (حد أدنى وأعلى موجبان).');
                if (input.inverterMaxVolt <= 0) errors.push('أقصى جهد للعاكس غير صالح.');
                if (input.targetSystemSize <= 0) errors.push('حجم النظام المستهدف يجب أن يكون موجبا.');

                if (errors.length) {
                    alertBox.style.display = 'flex';
                    alertBox.style.background = 'rgba(255,77,77,0.12)';
                    alertBox.style.borderColor = 'rgba(255,77,77,0.4)';
                    alertBox.innerHTML = `<span class="tool-note-icon">⚠️</span><div>${errors.join('<br>')}</div>`;
                    restoreSubmit();
                    return;
                }

                const calc = calculateAdvancedStringConfigurationLocal(input);

                if (!calc.optimalPanels) {
                    alertBox.style.display = 'flex';
                    alertBox.style.background = 'rgba(255,77,77,0.12)';
                    alertBox.style.borderColor = 'rgba(255,77,77,0.4)';
                    alertBox.innerHTML = `<span class="tool-note-icon">⚠️</span><div>لا يمكن إيجاد تكوين آمن بهذه المدخلات. راجع مواصفات اللوح أو نطاق العاكس.</div>`;
                    restoreSubmit();
                    return;
                }

                summaryCard.style.display = 'flex';
                summaryCard.innerHTML = `
                    <h4>نطاق الألواح لكل سلسلة</h4>
                    <div class="tool-results-grid cols-2">
                        <div class="tool-stat"><span>الحد الأدنى</span><strong>${calc.minPanels} لوح</strong></div>
                        <div class="tool-stat"><span>الحد الأقصى</span><strong>${calc.maxPanels} لوح</strong></div>
                        <div class="tool-stat"><span>العدد الموصى به</span><strong>${calc.optimalPanels} لوح</strong></div>
                        <div class="tool-stat"><span>الجهد عند أبرد يوم</span><strong>${formatNumber(calc.maxStringVocAtMinTemp)} فولت</strong></div>
                        <div class="tool-stat"><span>الجهد عند أحر يوم</span><strong>${formatNumber(calc.minStringVmpAtMaxTemp)} فولت</strong></div>
                    </div>
                    <div class="tool-tag">النطاق الآمن للسلسلة: ${calc.minPanels} - ${calc.maxPanels} لوح</div>
                `;

                const isCurrentSafe = calc.arrayConfig.totalCurrent <= input.inverterMaxCurrent;

                arrayCard.style.display = 'flex';
                arrayCard.innerHTML = `
                    <h4>تكوين المصفوفة الكاملة</h4>
                    <div class="tool-results-grid cols-2">
                        <div class="tool-stat"><span>إجمالي الألواح</span><strong>${calc.arrayConfig.totalPanels} لوح</strong></div>
                        <div class="tool-stat"><span>عدد السلاسل المتوازية</span><strong>${calc.arrayConfig.parallelStrings}</strong></div>
                        <div class="tool-stat"><span>التيار الإجمالي (مع عامل 1.25)</span><strong>${formatNumber(calc.arrayConfig.totalCurrent)} أمبير</strong></div>
                        <div class="tool-stat"><span>حد تيار العاكس</span><strong>${formatNumber(input.inverterMaxCurrent)} أمبير</strong></div>
                    </div>
                    <div class="tool-notice" style="margin-top:12px; ${isCurrentSafe ? '' : 'border-color: rgba(255, 77, 77, 0.6); background: rgba(255, 77, 77, 0.08);'}">
                        <span class="tool-note-icon">${isCurrentSafe ? '✅' : '⚠️'}</span>
                        <div>${isCurrentSafe ? 'التيار الكلي ضمن الحدود المسموح بها للعاكس.' : 'تحذير: التيار الكلي يتجاوز حد العاكس! فكر في توزيع مختلف للألواح أو اختيار عاكس بتيار أعلى.'}</div>
                    </div>
                `;

                reasoningCard.style.display = 'flex';
                reasoningCard.innerHTML = '<p>جاري توليد التحليل الذكي...</p>';

                try {
                    const reasoning = await fetchStringConfigurationReasoning(input, calc);
                    reasoningCard.innerHTML = reasoning
                        ? `<h4>تحليل هندسي</h4><p style="white-space:pre-line; text-align:right;">${reasoning}</p>`
                        : `<h4>تحليل هندسي</h4><p>تم الحساب بنجاح، لكن تعذر الحصول على شرح تفصيلي من الذكاء الاصطناعي.</p>`;
                } catch (error) {
                    reasoningCard.innerHTML = `<h4>تحليل هندسي</h4><p>تعذر الوصول إلى خدمة الذكاء الاصطناعي. يمكنك الاعتماد على النتائج الرقمية أعلاه أو المحاولة لاحقاً.</p>`;
                } finally {
                    restoreSubmit();
                }
            });

            return () => {
                hideCards();
            };
        }

        function renderToolSystemSizing(container) {
            container.innerHTML = `
                <div class="tool-section">
                    <h3>حاسبة الحجم التقني والمالي</h3>
                    <p>أدخل بيانات الحمل والتكاليف للحصول على تصميم مقترح للنظام الشمسي وتحليل العوائد المالية عبر سنوات التشغيل.</p>
                    <form id="systemSizingForm" class="tool-grid cols-3">
                        <div class="tool-input-group"><label>الاستهلاك اليومي (ك.و س)</label><input type="number" name="dailyConsumption" value="32" step="0.1" required></div>
                        <div class="tool-input-group"><label>الطلب الأقصى (ك.و)</label><input type="number" name="peakDemand" value="7.5" step="0.1"></div>
                        <div class="tool-input-group"><label>متوسط ساعات الشمس (PSH)</label><input type="number" name="sunHours" value="5.5" step="0.1" required></div>
                        <div class="tool-input-group"><label>معامل الأداء الكلي (%)</label><input type="number" name="performanceRatio" value="78" step="1"></div>
                        <div class="tool-input-group"><label>نسبة المبالغة في الـ DC (%)</label><input type="number" name="dcOversize" value="10" step="1"></div>
                        <div class="tool-input-group"><label>قدرة اللوح (واط)</label><input type="number" name="panelWatt" value="550" step="10"></div>
                        <div class="tool-input-group"><label>نسبة DC/AC</label><input type="number" name="dcAcRatio" value="1.15" step="0.01"></div>
                        <div class="tool-input-group"><label>ساعات الاستقلالية للبطارية</label><input type="number" name="autonomyHours" value="4" step="0.5"></div>
                        <div class="tool-input-group"><label>عمق التفريغ المسموح (%)</label><input type="number" name="batteryDod" value="80" step="1"></div>
                        <div class="tool-input-group"><label>كفاءة منظومة التخزين (%)</label><input type="number" name="batteryEfficiency" value="92" step="1"></div>
                        <div class="tool-input-group"><label>جهد النظام المستهدف (فولت)</label><input type="number" name="systemVoltage" value="48" step="1"></div>
                        <div class="tool-input-group"><label>تكلفة التركيب لكل ك.و ذروة (دينار)</label><input type="number" name="systemCostPerKw" value="650" step="10"></div>
                        <div class="tool-input-group"><label>نسبة الحوافز أو الدعم (%)</label><input type="number" name="incentivePercent" value="10" step="1"></div>
                        <div class="tool-input-group"><label>سعر الكيلوواط ساعة (دينار)</label><input type="number" name="tariff" value="0.12" step="0.01"></div>
                        <div class="tool-input-group"><label>تكاليف التشغيل والصيانة السنوية (%)</label><input type="number" name="omPercent" value="1.5" step="0.1"></div>
                        <div class="tool-input-group"><label>سنوات التمويل</label><input type="number" name="loanYears" value="5" step="1"></div>
                        <div class="tool-input-group"><label>فائدة التمويل السنوية (%)</label><input type="number" name="loanRate" value="6" step="0.1"></div>
                        <div class="tool-input-group"><label>الدفع المقدم (%)</label><input type="number" name="downPayment" value="20" step="1"></div>
                        <div class="tool-input-group"><label>معدل تدهور الإنتاج السنوي (%)</label><input type="number" name="degradation" value="0.7" step="0.1"></div>
                        <div class="tool-input-group"><label>عمر المشروع (سنوات)</label><input type="number" name="projectLife" value="20" step="1"></div>
                        <div class="tool-input-group"><label>معدل الخصم للتحليل (%)</label><input type="number" name="discountRate" value="7" step="0.1"></div>
                        <div class="tool-input-group"><label>معدل تضخم التعرفة السنوي (%)</label><input type="number" name="inflation" value="2.5" step="0.1"></div>
                        <button type="submit" class="tool-action-btn">احسب النظام والاستثمار</button>
                    </form>
                    <div class="tool-notice" id="systemSizingAlert" style="display:none;"></div>
                </div>
                <div class="tool-results-card" id="systemSizingSummary" style="display:none;"></div>
                <div class="tool-results-card" id="systemSizingFinancial" style="display:none;"></div>
                <div class="tool-results-card" id="systemSizingCashflow" style="display:none;"></div>
            `;
            const form = container.querySelector('#systemSizingForm');
            const alertBox = container.querySelector('#systemSizingAlert');
            const summaryCard = container.querySelector('#systemSizingSummary');
            const financialCard = container.querySelector('#systemSizingFinancial');
            const cashflowCard = container.querySelector('#systemSizingCashflow');

            const formatCurrency = (value) => `${formatNumber(value, 0)} دينار`;
            const formatKwh = (value) => `${formatNumber(value, 0)} ك.و س`;
            const formatKw = (value) => `${formatNumber(value, 2)} ك.و`;
            const formatPercent = (value) => `${formatNumber(value * 100, 1)}%`;

            function hideCards() {
                alertBox.style.display = 'none';
                summaryCard.style.display = 'none';
                financialCard.style.display = 'none';
                cashflowCard.style.display = 'none';
            }

            function calculateIRR(cashflows) {
                const npvAt = (rate) => {
                    if (rate <= -1) return Number.POSITIVE_INFINITY;
                    return cashflows.reduce((acc, cf, idx) => acc + cf / Math.pow(1 + rate, idx), 0);
                };
                let lower = -0.9;
                let upper = 1.5;
                let npvLower = npvAt(lower);
                let npvUpper = npvAt(upper);

                if (Number.isNaN(npvLower) || Number.isNaN(npvUpper) || npvLower * npvUpper > 0) {
                    return null;
                }

                for (let i = 0; i < 100; i++) {
                    const mid = (lower + upper) / 2;
                    const npvMid = npvAt(mid);
                    if (!Number.isFinite(npvMid)) {
                        return null;
                    }
                    if (Math.abs(npvMid) < 1e-6) {
                        return mid;
                    }
                    if (npvLower * npvMid < 0) {
                        upper = mid;
                        npvUpper = npvMid;
                    } else {
                        lower = mid;
                        npvLower = npvMid;
                    }
                }

                const finalRate = (lower + upper) / 2;
                const finalNpv = npvAt(finalRate);
                return Number.isFinite(finalNpv) ? finalRate : null;
            }

            hideCards();

            form.addEventListener('submit', (event) => {
                event.preventDefault();
                hideCards();

                const formData = Object.fromEntries(new FormData(form).entries());
                const errors = [];

                const dailyConsumption = Math.max(0, parseNumber(formData.dailyConsumption, 0));
                const peakDemand = Math.max(0, parseNumber(formData.peakDemand, 0));
                const sunHours = Math.max(0.5, parseNumber(formData.sunHours, 0));
                const performanceRatio = Math.min(0.92, Math.max(0.4, parseNumber(formData.performanceRatio, 78) / 100));
                const dcOversize = Math.max(0, parseNumber(formData.dcOversize, 10) / 100);
                const panelWatt = Math.max(50, parseNumber(formData.panelWatt, 550));
                const dcAcRatio = Math.max(0.5, parseNumber(formData.dcAcRatio, 1.15));
                const autonomyHours = Math.max(0, parseNumber(formData.autonomyHours, 4));
                const batteryDod = Math.min(0.95, Math.max(0.1, parseNumber(formData.batteryDod, 80) / 100));
                const batteryEfficiency = Math.min(0.98, Math.max(0.6, parseNumber(formData.batteryEfficiency, 92) / 100));
                const systemVoltage = Math.max(12, parseNumber(formData.systemVoltage, 48));
                const systemCostPerKw = Math.max(0, parseNumber(formData.systemCostPerKw, 650));
                const incentivePercent = Math.max(0, parseNumber(formData.incentivePercent, 0) / 100);
                const tariff = Math.max(0, parseNumber(formData.tariff, 0.12));
                const omPercent = Math.max(0, parseNumber(formData.omPercent, 1.5) / 100);
                const loanYears = Math.max(1, Math.round(parseNumber(formData.loanYears, 5)));
                const loanRate = Math.max(0, parseNumber(formData.loanRate, 6) / 100);
                const downPayment = Math.min(0.9, Math.max(0, parseNumber(formData.downPayment, 20) / 100));
                const degradation = Math.min(0.05, Math.max(0, parseNumber(formData.degradation, 0.7) / 100));
                const projectLife = Math.max(1, Math.round(parseNumber(formData.projectLife, 20)));
                const discountRate = Math.max(0, parseNumber(formData.discountRate, 7) / 100);
                const inflation = Math.max(0, parseNumber(formData.inflation, 2.5) / 100);

                if (dailyConsumption <= 0) errors.push('يرجى إدخال استهلاك يومي أكبر من صفر.');
                if (sunHours <= 0.5) errors.push('متوسط ساعات الشمس يجب أن يكون أكبر من 0.5 ساعة.');
                if (systemCostPerKw <= 0) errors.push('تكلفة التركيب لكل كيلوواط ذروة يجب أن تكون موجبة.');
                if (tariff <= 0) errors.push('سعر الكهرباء يجب أن يكون أكبر من صفر.');
                if (panelWatt < 100) errors.push('قدرة اللوح المدخلة منخفضة جداً.');

                if (errors.length) {
                    alertBox.style.display = 'flex';
                    alertBox.innerHTML = `<span class="tool-note-icon">⚠️</span><div>${errors.join('<br>')}</div>`;
                    return;
                }

                const annualConsumption = dailyConsumption * 365;
                const baseSystemKw = dailyConsumption / (sunHours * performanceRatio);
                const dcCapacityKw = baseSystemKw * (1 + dcOversize);
                const panelCount = Math.max(1, Math.round((dcCapacityKw * 1000) / panelWatt));
                const actualDcCapacityKw = (panelCount * panelWatt) / 1000;
                const acCapacityKw = actualDcCapacityKw / dcAcRatio;
                const inverterRatingKw = Math.max(acCapacityKw, peakDemand);
                const annualProduction = actualDcCapacityKw * sunHours * 365 * performanceRatio;
                const capacityFactor = annualProduction / (actualDcCapacityKw * 8760);

                const autonomyFraction = Math.min(autonomyHours / 24, 1);
                const autonomyEnergy = dailyConsumption * autonomyFraction;
                const batteryEnergyRequired = autonomyEnergy / (batteryDod * batteryEfficiency);
                const batteryCapacityKwh = batteryEnergyRequired;
                const batteryCapacityAh = (batteryCapacityKwh * 1000) / systemVoltage;

                const grossCapex = actualDcCapacityKw * systemCostPerKw;
                const incentiveValue = grossCapex * incentivePercent;
                const netInvestment = Math.max(grossCapex - incentiveValue, 0);
                const downPaymentValue = netInvestment * downPayment;
                const loanAmount = Math.max(netInvestment - downPaymentValue, 0);

                let annualLoanPayment = 0;
                if (loanAmount > 0) {
                    if (loanRate > 0) {
                        const factor = Math.pow(1 + loanRate, loanYears);
                        annualLoanPayment = loanAmount * (loanRate * factor) / (factor - 1);
                    } else {
                        annualLoanPayment = loanAmount / loanYears;
                    }
                }

                const annualSavingsYear1 = Math.min(annualConsumption, annualProduction) * tariff;
                const annualOMCostYear1 = netInvestment * omPercent;
                const netCashYear1 = annualSavingsYear1 - annualOMCostYear1 - annualLoanPayment;
                const paybackBase = annualSavingsYear1 - annualOMCostYear1;
                const simplePayback = paybackBase > 0 ? netInvestment / paybackBase : null;

                let cumulative = -netInvestment;
                let npv = -netInvestment;
                const cashflows = [-netInvestment];
                const projection = [];

                for (let year = 1; year <= projectLife; year++) {
                    const production = annualProduction * Math.pow(1 - degradation, year - 1);
                    const adjustedTariff = tariff * Math.pow(1 + inflation, year - 1);
                    const savings = Math.min(annualConsumption, production) * adjustedTariff;
                    const omCost = netInvestment * omPercent * Math.pow(1 + inflation, year - 1);
                    const loan = year <= loanYears ? annualLoanPayment : 0;
                    const netCash = savings - omCost - loan;

                    cumulative += netCash;
                    npv += netCash / Math.pow(1 + discountRate, year);
                    cashflows.push(netCash);

                    projection.push({
                        year,
                        production,
                        savings,
                        omCost,
                        loan,
                        netCash,
                        cumulative,
                    });
                }

                const breakeven = projection.find(item => item.cumulative >= 0);
                const breakevenYear = breakeven ? breakeven.year : null;
                const irr = calculateIRR(cashflows);
                const irrDisplay = irr !== null ? formatPercent(irr) : 'غير متاح';

                summaryCard.style.display = 'flex';
                summaryCard.innerHTML = `
                    <h4>المخرجات الفنية</h4>
                    <div class="tool-results-grid cols-3">
                        <div class="tool-stat"><span>قدرة النظام DC</span><strong>${formatKw(actualDcCapacityKw)}</strong></div>
                        <div class="tool-stat"><span>عدد الألواح المقترح</span><strong>${formatNumber(panelCount, 0)} لوح</strong></div>
                        <div class="tool-stat"><span>حجم العاكس المقترح</span><strong>${formatKw(inverterRatingKw)}</strong></div>
                        <div class="tool-stat"><span>الإنتاج السنوي المتوقع</span><strong>${formatKwh(annualProduction)}</strong></div>
                        <div class="tool-stat"><span>عامل السعة التقريبي</span><strong>${formatPercent(capacityFactor)}</strong></div>
                        <div class="tool-stat"><span>بطارية الاستقلالية</span><strong>${formatNumber(batteryCapacityKwh, 1)} ك.و س / ${formatNumber(batteryCapacityAh, 0)} آه @ ${formatNumber(systemVoltage, 0)} فولت</strong></div>
                    </div>
                    <div class="tool-note">تم تقريب عدد الألواح إلى أقرب عدد صحيح، وقد تختلف النتائج قليلاً حسب نوع اللوح والظروف الفعلية للموقع.</div>
                `;

                financialCard.style.display = 'flex';
                financialCard.innerHTML = `
                    <h4>المؤشرات المالية</h4>
                    <div class="tool-results-grid cols-3">
                        <div class="tool-stat"><span>التكلفة الإجمالية</span><strong>${formatCurrency(grossCapex)}</strong></div>
                        <div class="tool-stat"><span>صافي الاستثمار بعد الدعم</span><strong>${formatCurrency(netInvestment)}</strong></div>
                        <div class="tool-stat"><span>الدفع المقدم</span><strong>${formatCurrency(downPaymentValue)}</strong></div>
                        <div class="tool-stat"><span>قيمة التمويل</span><strong>${formatCurrency(loanAmount)}</strong></div>
                        <div class="tool-stat"><span>القسط السنوي</span><strong>${formatCurrency(annualLoanPayment)}</strong></div>
                        <div class="tool-stat"><span>التوفير في السنة الأولى</span><strong>${formatCurrency(annualSavingsYear1)}</strong></div>
                        <div class="tool-stat"><span>صافي التدفق في السنة الأولى</span><strong>${formatCurrency(netCashYear1)}</strong></div>
                        <div class="tool-stat"><span>فترة الاسترداد البسيطة</span><strong>${simplePayback ? `${formatNumber(simplePayback, 1)} سنة` : 'غير متحقق'}</strong></div>
                        <div class="tool-stat"><span>سنة التعادل النقدي</span><strong>${breakevenYear ? `السنة ${breakevenYear}` : 'لم تتحقق خلال مدة الدراسة'}</strong></div>
                        <div class="tool-stat"><span>القيمة الحالية الصافية (NPV)</span><strong>${formatCurrency(npv)}</strong></div>
                        <div class="tool-stat"><span>معدل العائد الداخلي (IRR)</span><strong>${irrDisplay}</strong></div>
                    </div>
                    <div class="tool-note">يتم احتساب التدفقات النقدية على أساس التعرفة المدخلة ونسبة تضخمها السنوي، مع مراعاة أقساط التمويل خلال فترة القرض.</div>
                `;

                const rows = projection.map(item => `
                    <tr>
                        <td>${item.year}</td>
                        <td>${formatNumber(item.production, 0)}</td>
                        <td>${formatCurrency(item.savings)}</td>
                        <td>${formatCurrency(item.omCost)}</td>
                        <td>${formatCurrency(item.loan)}</td>
                        <td>${formatCurrency(item.netCash)}</td>
                        <td>${formatCurrency(item.cumulative)}</td>
                    </tr>
                `).join('');

                cashflowCard.style.display = 'flex';
                cashflowCard.innerHTML = `
                    <h4>التدفقات النقدية (${projectLife} سنة)</h4>
                    <div class="tool-note">الأرقام مبنية على افتراض ثبات بقية العوامل، ويمكن تعديل المدخلات لإجراء سيناريوهات مختلفة.</div>
                    <div class="tool-table-wrapper">
                        <table class="tool-table">
                            <thead>
                                <tr>
                                    <th>السنة</th>
                                    <th>الإنتاج (ك.و س)</th>
                                    <th>التوفير</th>
                                    <th>التشغيل والصيانة</th>
                                    <th>أقساط التمويل</th>
                                    <th>صافي التدفق</th>
                                    <th>التدفق التراكمي</th>
                                </tr>
                            </thead>
                            <tbody>${rows}</tbody>
                        </table>
                    </div>
                `;

                alertBox.style.display = 'flex';
                alertBox.innerHTML = `<span class="tool-note-icon">ℹ️</span><div>تم حساب الحجم الفني والمؤشرات المالية بالاعتماد على القيم المدخلة. جرّب تغيير ساعات الشمس أو تكاليف النظام لدراسة حساسية النتائج.</div>`;
            });

            return () => {
                hideCards();
            };
        }
        function renderToolFieldInspector(container) {
            container.innerHTML = `
                <div class="tool-section">
                    <h3>المفتش الميداني الذكي</h3>
                    <label class="tool-upload" id="fieldInspectorUpload">
                        <input type="file" accept="image/*">
                        <span>انقر أو اسحب صورة لمصفوفة الألواح</span>
                        <span class="tool-pill">يدعم ملفات JPG / PNG حتى 10MB</span>
                    </label>
                    <img id="fieldInspectorPreview" class="tool-image-preview" style="display:none;" alt="Solar array preview">
                    <div class="tool-results-card" id="fieldInspectorReport" style="display:none;"></div>
                </div>
            `;

            const uploader = container.querySelector('#fieldInspectorUpload input');
            const preview = container.querySelector('#fieldInspectorPreview');
            const report = container.querySelector('#fieldInspectorReport');

            uploader.addEventListener('change', () => {
                const file = uploader.files?.[0];
                if (!file) return;
                if (file.size > 10 * 1024 * 1024) {
                    report.style.display = 'flex';
                    report.innerHTML = '<h4>الملف كبير جداً</h4><p>الرجاء اختيار صورة أصغر من 10MB.</p>';
                    return;
                }

                const reader = new FileReader();
                reader.onload = (e) => {
                    preview.src = e.target?.result;
                    preview.style.display = 'block';
                    report.style.display = 'flex';
                    report.innerHTML = '<p>جاري تحليل الصورة...</p>';

                    setTimeout(() => analyzeImage(preview, report), 120);
                };
                reader.readAsDataURL(file);
            });

            function analyzeImage(imageElement, reportNode) {
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                const width = Math.min(imageElement.naturalWidth || 600, 600);
                const height = Math.round((imageElement.naturalHeight || 400) * (width / (imageElement.naturalWidth || 600)));
                canvas.width = width;
                canvas.height = height;
                context.drawImage(imageElement, 0, 0, width, height);
                const data = context.getImageData(0, 0, width, height).data;

                let total = 0;
                let variance = 0;
                const sampleStep = 4 * 8;
                let count = 0;
                for (let i = 0; i < data.length; i += sampleStep) {
                    const r = data[i];
                    const g = data[i + 1];
                    const b = data[i + 2];
                    const brightness = 0.299 * r + 0.587 * g + 0.114 * b;
                    total += brightness;
                    variance += brightness * brightness;
                    count++;
                }

                const mean = total / count;
                const stdDev = Math.sqrt(Math.max(variance / count - mean * mean, 0));
                const shadingRisk = mean < 95;
                const uniformityRisk = stdDev < 28;

                const findings = [];
                if (shadingRisk) {
                    findings.push('تشير مستويات السطوع المنخفضة إلى احتمال وجود غبار كثيف أو ظلال على المصفوفة.');
                } else {
                    findings.push('سطوع الألواح ضمن المستوى الطبيعي، لا توجد مؤشرات واضحة على الغبار.');
                }
                if (uniformityRisk) {
                    findings.push('التباين اللوني منخفض، قد يعني هذا أن الصورة التُقطت في ظروف إضاءة ضعيفة أو أن هناك جزءاً من المصفوفة مظلل بالكامل.');
                } else {
                    findings.push('التباين جيد، توزيع الإضاءة يبدو متوازناً بين الألواح.');
                }

                reportNode.innerHTML = `
                    <h4>تقرير الفحص المبدئي</h4>
                    <ul style="margin:0; padding-inline-start:20px; display:flex; flex-direction:column; gap:8px; font-size:14px;">
                        ${findings.map(item => `<li>${item}</li>`).join('')}
                    </ul>
                    <div class="tool-notice" style="margin-top:12px;">
                        <span class="tool-note-icon">📷</span>
                        <div>نتيجة تحليل سريعة تعتمد على متوسط سطوع الصورة. للحصول على تشخيص دقيق، يُنصح برفع الصورة إلى النسخة السحابية من المفتش الميداني.</div>
                    </div>
                `;
            }
        }


        function renderToolAreaCalculator(container) {
            if (!container) {
                return;
            }

            container.innerHTML = `
                <div class="tool-section">
                    <h3>حاسبة المساحة والإنتاج</h3>
                    <p>ارسم حدود السطح على الخريطة أو أدخل الأبعاد يدويًا لمعرفة عدد الألواح والإنتاج المتوقع.</p>
                    <form id="areaCalculatorForm" class="tool-grid cols-3">
                        <div class="tool-input-group"><label>عرض الموقع المتاح (متر)</label><input type="number" name="landWidth" value="20" step="0.1" min="1" required></div>
                        <div class="tool-input-group"><label>طول الموقع المتاح (متر)</label><input type="number" name="landLength" value="30" step="0.1" min="1" required></div>
                        <div class="tool-input-group"><label>عرض اللوح (متر)</label><input type="number" name="panelWidth" value="1.13" step="0.01" min="0.1" required></div>
                        <div class="tool-input-group"><label>طول اللوح (متر)</label><input type="number" name="panelLength" value="2.28" step="0.01" min="0.1" required></div>
                        <div class="tool-input-group"><label>قدرة اللوح (واط)</label><input type="number" name="panelWattage" value="550" step="10" min="50" required></div>
                        <div class="tool-input-group"><label>متوسط ساعات الشمس اليومية</label><input type="number" name="sunHours" value="5.5" step="0.1" min="1" max="12" required></div>
                        <div class="tool-input-group" style="grid-column: span 3;">
                            <label>اتجاه الألواح</label>
                            <div style="display:flex; gap:20px; flex-wrap:wrap; font-size:13px;">
                                <label style="display:flex; align-items:center; gap:6px;"><input type="radio" name="orientation" value="auto" checked> تلقائي</label>
                                <label style="display:flex; align-items:center; gap:6px;"><input type="radio" name="orientation" value="portrait"> طولي</label>
                                <label style="display:flex; align-items:center; gap:6px;"><input type="radio" name="orientation" value="landscape"> عرضي</label>
                            </div>
                        </div>
                        <div class="tool-input-group" style="grid-column: span 3;">
                            <label>ملاحظات أو قيود خاصة (اختياري)</label>
                            <textarea name="notes" rows="2" placeholder="مثال: ترك ممر خدمة أو تجنب عوائق محددة."></textarea>
                        </div>
                        <button type="submit" class="tool-action-btn" style="grid-column: span 3;">احسب خطة التوزيع</button>
                    </form>
                </div>
                <div class="tool-section">
                    <h4>حدد المساحة على الخريطة</h4>
                    <p style="margin:0; color:var(--text-secondary); font-size:13px;">استخدم أزرار الرسم في أعلى الخريطة لرسم مضلع يمثل السطح. يمكن تعديل الشكل أو حذفه في أي وقت.</p>
                    <div id="areaCalculatorMap" class="area-calculator-map" style="height:300px; margin-top:12px; border-radius:12px; overflow:hidden; background:rgba(255,255,255,0.05); display:flex; align-items:center; justify-content:center; color:var(--text-secondary);">
                        <div>جاري تحميل الخريطة...</div>
                    </div>
                    <div class="tool-report-actions" style="margin-top:12px; gap:12px;">
                        <button type="button" class="tool-action-btn light" id="areaMapReset">إعادة تعيين الرسم</button>
                    </div>
                </div>
                <div class="tool-notice" id="areaCalculatorAlert" style="display:none;"></div>
                <div class="tool-results-card" id="areaCalculatorResults" style="display:none;"></div>
            `;

            const form = container.querySelector('#areaCalculatorForm');
            const alertBox = container.querySelector('#areaCalculatorAlert');
            const resultsCard = container.querySelector('#areaCalculatorResults');
            const mapElement = container.querySelector('#areaCalculatorMap');
            const resetMapBtn = container.querySelector('#areaMapReset');

            const orientationText = {
                portrait: 'طولي (Portrait)',
                landscape: 'عرضي (Landscape)'
            };

            let mapInstance = null;
            let drawnItems = null;

            function showAlert(message, variant = 'info') {
                if (!alertBox) {
                    return;
                }
                alertBox.style.display = 'flex';
                alertBox.style.background = variant === 'error'
                    ? 'rgba(255,77,77,0.12)'
                    : 'rgba(255,255,255,0.06)';
                alertBox.style.borderColor = variant === 'error'
                    ? 'rgba(255,77,77,0.35)'
                    : 'rgba(255,255,255,0.1)';
                alertBox.innerHTML = `<span class="tool-note-icon">${variant === 'error' ? '⚠️' : 'ℹ️'}</span><div>${message}</div>`;
            }

            function latLngDistanceMeters(a, b) {
                const rad = Math.PI / 180;
                const dLat = (b.lat - a.lat) * rad;
                const dLng = (b.lng - a.lng) * rad;
                const sinLat = Math.sin(dLat / 2);
                const sinLng = Math.sin(dLng / 2);
                const h = sinLat * sinLat + Math.cos(a.lat * rad) * Math.cos(b.lat * rad) * sinLng * sinLng;
                return 2 * 6378137 * Math.asin(Math.sqrt(h));
            }

            function updateFieldsFromBounds(bounds, area) {
                const centerLat = bounds.getCenter().lat;
                const west = bounds.getWest();
                const east = bounds.getEast();
                const south = bounds.getSouth();
                const north = bounds.getNorth();
                const width = latLngDistanceMeters({ lat: centerLat, lng: west }, { lat: centerLat, lng: east });
                const length = latLngDistanceMeters({ lat: south, lng: (west + east) / 2 }, { lat: north, lng: (west + east) / 2 });
                const widthField = form.elements['landWidth'];
                const lengthField = form.elements['landLength'];
                if (widthField && width > 0) {
                    widthField.value = width.toFixed(1);
                }
                if (lengthField && length > 0) {
                    lengthField.value = length.toFixed(1);
                }
                showAlert(`المساحة المرسومة تقريبًا ${formatNumber(area, 0)} م². تم تحديث الأبعاد بناءً على حدود الشكل.`);
            }

            function calculatePolygonArea(latlngs) {
                // Fallback area calculation using shoelace formula
                if (!latlngs || latlngs.length < 3) return 0;

                let area = 0;
                const n = latlngs.length;

                for (let i = 0; i < n; i++) {
                    const j = (i + 1) % n;
                    area += latlngs[i].lat * latlngs[j].lng;
                    area -= latlngs[j].lat * latlngs[i].lng;
                }

                // Convert to square meters (approximate)
                const earthRadius = 6378137; // meters
                const degToRad = Math.PI / 180;
                area = Math.abs(area) / 2 * degToRad * degToRad * earthRadius * earthRadius;

                return area;
            }

            function processLayer(layer) {
                if (!mapInstance) {
                    return;
                }
                let latlngs = layer.getLatLngs();
                latlngs = Array.isArray(latlngs[0]) ? latlngs[0] : latlngs;
                if (!latlngs || !latlngs.length) {
                    return;
                }

                let area;
                if (typeof L.GeometryUtil !== 'undefined' && L.GeometryUtil.geodesicArea) {
                    area = Math.abs(L.GeometryUtil.geodesicArea(latlngs));
                } else {
                    area = calculatePolygonArea(latlngs);
                }
                if (area <= 0) {
                    showAlert('تعذر حساب المساحة، يرجى إعادة الرسم.', 'error');
                    return;
                }
                const bounds = layer.getBounds();
                updateFieldsFromBounds(bounds, area);
            }

            function initMap() {
                if (!mapElement) {
                    return;
                }
                if (typeof L === 'undefined') {
                    mapElement.innerHTML = '<p style="margin:12px 0; color: var(--text-secondary);">تعذر تحميل مكتبة الخريطة. تأكد من الاتصال بالإنترنت.</p>';
                    return;
                }

                if (typeof L.Draw === 'undefined') {
                    mapElement.innerHTML = '<p style="margin:12px 0; color: var(--text-secondary);">تعذر تحميل أدوات الرسم. تأكد من الاتصال بالإنترنت وحاول إعادة تحميل الصفحة.</p>';
                    return;
                }

                // Clear loading message
                mapElement.innerHTML = '';
                mapElement.style.display = 'block';
                mapElement.style.alignItems = 'unset';
                mapElement.style.justifyContent = 'unset';

                mapInstance = L.map(mapElement).setView([31.9539, 35.9106], 15);
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    maxZoom: 19,
                    attribution: '&copy; OpenStreetMap contributors',
                }).addTo(mapInstance);

                drawnItems = new L.FeatureGroup();
                mapInstance.addLayer(drawnItems);

                const drawControl = new L.Control.Draw({
                    position: 'topright',
                    draw: {
                        polygon: {
                            allowIntersection: false,
                            showArea: true,
                            metric: ['km', 'm'],
                            shapeOptions: { color: '#22d3ee' },
                        },
                        rectangle: {
                            shapeOptions: { color: '#38bdf8' },
                        },
                        polyline: false,
                        circle: false,
                        marker: false,
                        circlemarker: false,
                    },
                    edit: {
                        featureGroup: drawnItems,
                        remove: true,
                    },
                });
                mapInstance.addControl(drawControl);

                mapInstance.on(L.Draw.Event.CREATED, (event) => {
                    drawnItems.clearLayers();
                    drawnItems.addLayer(event.layer);
                    processLayer(event.layer);
                });

                mapInstance.on(L.Draw.Event.EDITED, (event) => {
                    const layers = event.layers.getLayers();
                    if (layers.length) {
                        processLayer(layers[0]);
                    }
                });

                mapInstance.on(L.Draw.Event.DELETED, () => {
                    if (alertBox) {
                        alertBox.style.display = 'none';
                    }
                });

                setTimeout(() => mapInstance.invalidateSize(), 300);
            }

            if (resetMapBtn) {
                resetMapBtn.addEventListener('click', () => {
                    if (drawnItems) {
                        drawnItems.clearLayers();
                    }
                    if (alertBox) {
                        alertBox.style.display = 'none';
                    }
                });
            }

            // Initialize map with a small delay to ensure DOM is ready
            setTimeout(() => {
                initMap();
            }, 100);

            form.addEventListener('submit', (event) => {
                event.preventDefault();
                if (alertBox) {
                    alertBox.style.display = 'none';
                    alertBox.innerHTML = '';
                }

                const data = new FormData(form);
                const values = {
                    landWidth: parseNumber(data.get('landWidth'), 0),
                    landLength: parseNumber(data.get('landLength'), 0),
                    panelWidth: parseNumber(data.get('panelWidth'), 0),
                    panelLength: parseNumber(data.get('panelLength'), 0),
                    panelWattage: parseNumber(data.get('panelWattage'), 0),
                    sunHours: parseNumber(data.get('sunHours'), 0),
                    orientation: data.get('orientation') || 'auto',
                    notes: (data.get('notes') || '').toString().trim(),
                };

                const errors = [];
                if (values.landWidth <= 0 || values.landLength <= 0) errors.push('أدخل أبعاد الموقع بالأمتار بشكل صحيح.');
                if (values.panelWidth <= 0 || values.panelLength <= 0) errors.push('أدخل أبعاد اللوح بشكل صحيح.');
                if (values.panelWattage <= 0) errors.push('قدرة اللوح يجب أن تكون موجبة.');
                if (values.sunHours <= 0) errors.push('متوسط ساعات الشمس يجب أن يكون أكبر من صفر.');

                if (errors.length) {
                    showAlert(errors.join('<br>'), 'error');
                    if (resultsCard) {
                        resultsCard.style.display = 'none';
                        resultsCard.innerHTML = '';
                    }
                    return;
                }

                const calculation = calculateAreaProductionLocal(values);
                const siteArea = values.landWidth * values.landLength;
                const orientationLabel = orientationText[calculation.finalOrientation] || 'تلقائي';

                const productionNotice = values.sunHours >= 7
                    ? 'الموقع يتمتع بإشعاع مرتفع، تأكد من وجود تهوية جيدة لتقليل تأثير الحرارة.'
                    : 'يمكن تحسين الإنتاج بإمالة 30° باتجاه الجنوب وتقليل الظلال.';

                if (resultsCard) {
                    resultsCard.style.display = 'flex';
                    resultsCard.innerHTML = `
                        <div class="tool-section">
                            <h4>معطيات المساحة</h4>
                            <div class="tool-results-grid cols-3">
                                <div class="tool-stat"><span>المساحة الكلية</span><strong>${formatNumber(siteArea, 1)} م²</strong></div>
                                <div class="tool-stat"><span>عدد الصفوف المحتملة</span><strong>${calculation.rowCount}</strong></div>
                                <div class="tool-stat"><span>ألواح في كل صف</span><strong>${calculation.panelsPerString}</strong></div>
                            </div>
                            <div class="tool-notice" style="margin-top:12px;">
                                <span class="tool-note-icon">📐</span>
                                <div>تم استخدام معامل تباعد 1.5 بين الصفوف لحساب المساحة الآمنة والتقليل من التظليل الذاتي.</div>
                            </div>
                        </div>

                        <div class="tool-section">
                            <h4>أفضل توزيع للألواح</h4>
                            <div class="tool-results-grid cols-3">
                                <div class="tool-stat"><span>عدد الألواح الممكن</span><strong>${calculation.maxPanels} لوح</strong></div>
                                <div class="tool-stat"><span>التوزيع المقترح</span><strong>${orientationLabel}</strong></div>
                                <div class="tool-stat"><span>المساحة لكل لوح</span><strong>${formatNumber(values.panelWidth * values.panelLength, 2)} م²</strong></div>
                            </div>
                        </div>

                        <div class="tool-section">
                            <h4>الإنتاج المتوقع</h4>
                            <div class="tool-results-grid cols-3">
                                <div class="tool-stat"><span>قدرة النظام DC</span><strong>${formatNumber(calculation.totalPowerKw, 2)} ك.و</strong></div>
                                <div class="tool-stat"><span>الإنتاج اليومي</span><strong>${formatNumber(calculation.dailyEnergyKwh, 1)} ك.و س</strong></div>
                                <div class="tool-stat"><span>الإنتاج السنوي</span><strong>${formatNumber(calculation.yearlyEnergyKwh, 0)} ك.و س</strong></div>
                            </div>
                            <div class="tool-notice" style="margin-top:12px;">
                                <span class="tool-note-icon">☀️</span>
                                <div>${productionNotice}</div>
                            </div>
                        </div>

                        ${values.notes ? `<div class="tool-section"><h4>ملاحظات المستخدم</h4><p style="margin:0;">${values.notes}</p></div>` : ''}

                        <div class="tool-section">
                            <div class="tool-report-actions" style="display:flex; gap:12px; flex-wrap:wrap;">
                                <button type="button" class="tool-action-btn" id="areaCalculatorAddReport">أضف النتائج إلى التقرير</button>
                            </div>
                        </div>
                    `;

                    const addReportBtn = resultsCard.querySelector('#areaCalculatorAddReport');
                    if (addReportBtn) {
                        addReportBtn.addEventListener('click', () => {
                            window.energyReport.addCard({
                                type: 'تخطيط المساحة والإنتاج',
                                summary: `موقع بمساحة ${formatNumber(siteArea, 1)} م² يتسع لـ ${calculation.maxPanels} لوح`,
                                values: {
                                    'قدرة النظام DC': `${formatNumber(calculation.totalPowerKw, 2)} ك.و`,
                                    'إنتاج يومي تقديري': `${formatNumber(calculation.dailyEnergyKwh, 1)} ك.و س`,
                                    'عدد الألواح': `${calculation.maxPanels} لوح`,
                                    'التوزيع المقترح': orientationLabel,
                                }
                            });
                        });
                    }
                }
            });

            return () => {
                if (mapInstance) {
                    mapInstance.off();
                    mapInstance.remove();
                    mapInstance = null;
                }
            };
        }
        function renderToolBatteryStorage(container) {
            if (!container) {
                return;
            }

            container.innerHTML = `
                <div class="tool-section">
                    <h3>حاسبة تخزين الطاقة</h3>
                    <p>حدد سعة بنك البطاريات وعددها بناءً على الحمل اليومي المطلوب وأيام الاستقلالية ونوع البطارية.</p>
                    <form id="batteryStorageForm" class="tool-grid cols-3">
                        <div class="tool-input-group"><label>الحمل اليومي (ك.و س)</label><input type="number" name="dailyLoadKwh" value="10" step="0.1" min="0.5" required></div>
                        <div class="tool-input-group"><label>أيام الاستقلالية</label><input type="number" name="autonomyDays" value="1" step="0.5" min="0.5" required></div>
                        <div class="tool-input-group"><label>عمق التفريغ المسموح (%)</label><input type="number" name="depthOfDischarge" value="80" step="1" min="10" max="95" required></div>
                        <div class="tool-input-group"><label>جهد البطارية (فولت)</label><input type="number" name="batteryVoltage" value="12" step="1" min="2" required></div>
                        <div class="tool-input-group"><label>سعة البطارية (أمبير.ساعة)</label><input type="number" name="batteryCapacityAh" value="200" step="5" min="20" required></div>
                        <div class="tool-input-group"><label>جهد النظام المطلوب (فولت)</label><input type="number" name="systemVoltage" value="48" step="1" min="12" required></div>
                        <div class="tool-input-group" style="grid-column: span 3;">
                            <label>إضافة أجهزة تفصيلية (اختياري)</label>
                            <div id="batteryApplianceList" style="display:flex; flex-direction:column; gap:12px;"></div>
                            <button type="button" class="tool-action-btn light" id="addApplianceRow" style="margin-top:12px;">إضافة جهاز استهلاك</button>
                            <div class="tool-notice" id="applianceTotalNote" style="display:none; margin-top:12px;"></div>
                        </div>
                        <button type="submit" class="tool-action-btn" style="grid-column: span 3;">احسب بنك البطاريات</button>
                    </form>
                </div>
                <div class="tool-notice" id="batteryStorageAlert" style="display:none;"></div>
                <div class="tool-results-card" id="batteryStorageResults" style="display:none;"></div>
            `;

            const form = container.querySelector('#batteryStorageForm');
            const alertBox = container.querySelector('#batteryStorageAlert');
            const results = container.querySelector('#batteryStorageResults');
            const addApplianceBtn = container.querySelector('#addApplianceRow');
            const applianceList = container.querySelector('#batteryApplianceList');
            const applianceNote = container.querySelector('#applianceTotalNote');
            const dailyLoadInput = form.querySelector('input[name="dailyLoadKwh"]');
            const applianceRows = [];

            function updateApplianceTotals() {
                const total = applianceRows.reduce((sum, row) => {
                    const power = parseNumber(row.querySelector('input[name="appliancePower"]').value, 0);
                    const qty = parseNumber(row.querySelector('input[name="applianceQty"]').value, 0);
                    const hours = parseNumber(row.querySelector('input[name="applianceHours"]').value, 0);
                    if (power > 0 && qty > 0 && hours > 0) {
                        return sum + (power * qty * hours) / 1000;
                    }
                    return sum;
                }, 0);

                if (total > 0) {
                    if (dailyLoadInput) {
                        dailyLoadInput.value = total.toFixed(2);
                    }
                    if (applianceNote) {
                        applianceNote.style.display = 'flex';
                        applianceNote.innerHTML = `<span class="tool-note-icon">ℹ️</span><div>تم احتساب الحمل اليومي تلقائياً من الأجهزة المدرجة: ${formatNumber(total, 2)} ك.و س/يوم.</div>`;
                    }
                } else if (applianceNote) {
                    applianceNote.style.display = 'none';
                    applianceNote.innerHTML = '';
                }
            }

            function addApplianceRow() {
                const row = document.createElement('div');
                row.style.display = 'grid';
                row.style.gridTemplateColumns = 'minmax(0,2fr) repeat(3,minmax(0,1fr)) auto';
                row.style.alignItems = 'center';
                row.style.gap = '8px';
                row.innerHTML = `
                    <input type="text" name="applianceName" placeholder="اسم الجهاز" style="padding:8px; border-radius:8px; border:1px solid rgba(255,255,255,0.15); background:rgba(255,255,255,0.05); color:var(--text-primary);">
                    <input type="number" name="appliancePower" placeholder="القدرة (واط)" min="1" step="10" style="padding:8px; border-radius:8px; border:1px solid rgba(255,255,255,0.15); background:rgba(255,255,255,0.05); color:var(--text-primary);">
                    <input type="number" name="applianceQty" placeholder="الكمية" min="1" step="1" style="padding:8px; border-radius:8px; border:1px solid rgba(255,255,255,0.15); background:rgba(255,255,255,0.05); color:var(--text-primary);">
                    <input type="number" name="applianceHours" placeholder="ساعات التشغيل" min="0.1" step="0.1" style="padding:8px; border-radius:8px; border:1px solid rgba(255,255,255,0.15); background:rgba(255,255,255,0.05); color:var(--text-primary);">
                    <button type="button" class="tool-action-btn light" data-remove>إزالة</button>
                `;
                applianceList.appendChild(row);
                applianceRows.push(row);

                row.querySelectorAll('input').forEach(input => input.addEventListener('input', updateApplianceTotals));
                const removeBtn = row.querySelector('[data-remove]');
                if (removeBtn) {
                    removeBtn.addEventListener('click', () => {
                        const index = applianceRows.indexOf(row);
                        if (index !== -1) {
                            applianceRows.splice(index, 1);
                        }
                        row.remove();
                        updateApplianceTotals();
                    });
                }
            }

            if (addApplianceBtn) {
                addApplianceBtn.addEventListener('click', addApplianceRow);
            }

            form.addEventListener('submit', (event) => {
                event.preventDefault();
                if (alertBox) {
                    alertBox.style.display = 'none';
                    alertBox.innerHTML = '';
                }

                const data = new FormData(form);
                const appliances = applianceRows.map(row => ({
                    name: row.querySelector('input[name="applianceName"]').value.trim(),
                    power: parseNumber(row.querySelector('input[name="appliancePower"]').value, 0),
                    quantity: parseNumber(row.querySelector('input[name="applianceQty"]').value, 0),
                    hours: parseNumber(row.querySelector('input[name="applianceHours"]').value, 0),
                })).filter(item => item.power > 0 && item.quantity > 0 && item.hours > 0);

                const values = {
                    dailyLoadKwh: parseNumber(data.get('dailyLoadKwh'), 0),
                    autonomyDays: parseNumber(data.get('autonomyDays'), 0),
                    depthOfDischarge: parseNumber(data.get('depthOfDischarge'), 0),
                    batteryVoltage: parseNumber(data.get('batteryVoltage'), 0),
                    batteryCapacityAh: parseNumber(data.get('batteryCapacityAh'), 0),
                    systemVoltage: parseNumber(data.get('systemVoltage'), 0),
                    appliances,
                };

                const errors = [];
                if (values.dailyLoadKwh <= 0) errors.push('أدخل الحمل اليومي بالكيلوواط ساعة.');
                if (values.autonomyDays <= 0) errors.push('أدخل عدد أيام الاستقلالية المطلوبة.');
                if (values.depthOfDischarge <= 0 || values.depthOfDischarge > 100) errors.push('نسبة عمق التفريغ يجب أن تكون بين 10% و95%.');
                if (values.batteryVoltage <= 0 || values.batteryCapacityAh <= 0 || values.systemVoltage <= 0) errors.push('أدخل قيم البطارية وجهد النظام بشكل صحيح.');
                if (values.systemVoltage % values.batteryVoltage !== 0) errors.push('يجب أن يكون جهد النظام من مضاعفات جهد البطارية الواحدة.');

                if (errors.length) {
                    if (alertBox) {
                        alertBox.style.display = 'flex';
                        alertBox.innerHTML = `<span class="tool-note-icon">⚠️</span><div>${errors.join('<br>')}</div>`;
                    }
                    if (results) {
                        results.style.display = 'none';
                        results.innerHTML = '';
                    }
                    return;
                }

                const calculation = calculateBatteryBankLocal(values);

                if (results) {
                    results.style.display = 'flex';
                    results.innerHTML = `
                        <div class="tool-section">
                            <h4>سعة بنك البطاريات المطلوبة</h4>
                            <div class="tool-results-grid cols-3">
                                <div class="tool-stat"><span>الطاقة المتاحة</span><strong>${formatNumber(calculation.requiredBankEnergyKwh, 2)} ك.و س</strong></div>
                                <div class="tool-stat"><span>السعة عند جهد النظام</span><strong>${formatNumber(calculation.requiredBankCapacityAh, 1)} أمبير.ساعة</strong></div>
                                <div class="tool-stat"><span>الحمل اليومي المستخدم</span><strong>${formatNumber(calculation.finalDailyLoadKwh, 2)} ك.و س</strong></div>
                            </div>
                        </div>

                        <div class="tool-section">
                            <h4>تكوين البطاريات</h4>
                            <div class="tool-results-grid cols-3">
                                <div class="tool-stat"><span>بطاريات على التوالي</span><strong>${calculation.batteriesInSeries}</strong></div>
                                <div class="tool-stat"><span>سلاسل متوازية</span><strong>${calculation.parallelStrings}</strong></div>
                                <div class="tool-stat"><span>إجمالي البطاريات</span><strong>${calculation.totalBatteries}</strong></div>
                            </div>
                        </div>

                        <div class="tool-section">
                            <div class="tool-notice" style="margin-bottom:12px;">
                                <span class="tool-note-icon">⚡</span>
                                <div>راجع بيانات البطارية (Datasheet) وحدود الشاحن للتأكد من التوافق، ووازن الأحمال على السلاسل لضمان عمر أطول للبطاريات.</div>
                            </div>
                            <div class="tool-report-actions" style="display:flex; gap:12px; flex-wrap:wrap;">
                                <button type="button" class="tool-action-btn" id="batteryAddToReport">أضف النتائج إلى التقرير</button>
                            </div>
                        </div>
                    `;

                    const addReportBtn = results.querySelector('#batteryAddToReport');
                    if (addReportBtn) {
                        addReportBtn.addEventListener('click', () => {
                            window.energyReport.addCard({
                                type: 'تصميم بنك البطاريات',
                                summary: `بنك ${calculation.totalBatteries} بطارية لتغطية ${formatNumber(calculation.finalDailyLoadKwh, 2)} ك.و س/يوم`,
                                values: {
                                    'الطاقة المخزنة': `${formatNumber(calculation.requiredBankEnergyKwh, 2)} ك.و س`,
                                    'عدد البطاريات في السلسلة': `${calculation.batteriesInSeries}`,
                                    'عدد السلاسل المتوازية': `${calculation.parallelStrings}`,
                                    'إجمالي البطاريات': `${calculation.totalBatteries}`,
                                }
                            });
                        });
                    }
                }
            });
        }


        function renderToolWireSizing(container) {
            if (!container) {
                return;
            }

            container.innerHTML = `
                <div class="tool-section">
                    <h3>حاسبة مقطع السلك (دوائر التيار المستمر)</h3>
                    <p>تعتمد هذه الحاسبة على طريقة هبوط الجهد لتحديد مساحة مقطع السلك الملائمة من أجل دوائر الطاقة الشمسية.</p>
                    <form id="wireSizingForm" class="tool-grid cols-3">
                        <div class="tool-input-group"><label>التيار (أمبير)</label><input type="number" name="current" value="25" step="0.5" min="0.5" required></div>
                        <div class="tool-input-group"><label>الجهد (فولت)</label><input type="number" name="voltage" value="600" step="10" min="50" required></div>
                        <div class="tool-input-group"><label>طول المسار الواحد (متر)</label><input type="number" name="distance" value="30" step="0.5" min="1" required></div>
                        <div class="tool-input-group"><label>نسبة هبوط الجهد المسموحة (%)</label><input type="number" name="voltageDropPercentage" value="2" step="0.1" min="0.5" max="5" required></div>
                        <button type="submit" class="tool-action-btn" style="grid-column: span 3;">احسب مساحة المقطع</button>
                    </form>
                </div>
                <div class="tool-notice" id="wireSizingAlert" style="display:none;"></div>
                <div class="tool-results-card" id="wireSizingResults" style="display:none;"></div>
            `;

            const form = container.querySelector('#wireSizingForm');
            const alertBox = container.querySelector('#wireSizingAlert');
            const results = container.querySelector('#wireSizingResults');

            form.addEventListener('submit', (event) => {
                event.preventDefault();
                if (alertBox) {
                    alertBox.style.display = 'none';
                    alertBox.innerHTML = '';
                }

                const data = new FormData(form);
                const values = {
                    current: parseNumber(data.get('current'), 0),
                    voltage: parseNumber(data.get('voltage'), 0),
                    distance: parseNumber(data.get('distance'), 0),
                    voltageDropPercentage: parseNumber(data.get('voltageDropPercentage'), 0),
                };

                const errors = [];
                if (values.current <= 0) errors.push('أدخل قيمة التيار بالأمبير.');
                if (values.voltage <= 0) errors.push('أدخل قيمة الجهد للفصل.');
                if (values.distance <= 0) errors.push('أدخل طول المسار بالمتر.');
                if (values.voltageDropPercentage <= 0) errors.push('نسبة هبوط الجهد يجب أن تكون أكبر من صفر.');

                if (errors.length) {
                    if (alertBox) {
                        alertBox.style.display = 'flex';
                        alertBox.innerHTML = `<span class="tool-note-icon">⚠️</span><div>${errors.join('<br>')}</div>`;
                    }
                    if (results) {
                        results.style.display = 'none';
                        results.innerHTML = '';
                    }
                    return;
                }

                const suggestion = calculateWireSizeLocal(values);
                const reasoning = `عند تيار ${formatNumber(values.current, 1)} أمبير ومسار طوله ${formatNumber(values.distance, 1)} متر، فإن استخدام سلك بمساحة ${suggestion.recommendedWireSizeMM2} مم² يبقي هبوط الجهد قرب ${formatNumber(suggestion.voltageDrop, 2)} فولت (≈ ${formatNumber(values.voltageDropPercentage, 1)}%). هذا يضمن الفقد الحراري ${formatNumber(suggestion.powerLoss, 2)} وات فقط على المسار.`;

                if (results) {
                    results.style.display = 'flex';
                    results.innerHTML = `
                        <div class="tool-section">
                            <h4>المقترح الفيزيائي</h4>
                            <div class="tool-results-grid cols-2">
                                <div class="tool-stat"><span>مساحة المقطع الموصى بها</span><strong>${suggestion.recommendedWireSizeMM2} مم²</strong></div>
                                <div class="tool-stat"><span>هبوط الجهد المتوقع</span><strong>${formatNumber(suggestion.voltageDrop, 2)} فولت</strong></div>
                                <div class="tool-stat"><span>فقد الطاقة المقدر</span><strong>${formatNumber(suggestion.powerLoss, 2)} وات</strong></div>
                                <div class="tool-stat"><span>الهامش المستخدم</span><strong>${formatNumber(values.voltageDropPercentage, 1)}%</strong></div>
                            </div>
                        </div>

                        <div class="tool-section">
                            <div class="tool-notice" style="margin-bottom:12px;">
                                <span class="tool-note-icon">💡</span>
                                <div>${reasoning}</div>
                            </div>
                            <div class="tool-report-actions" style="display:flex; gap:12px; flex-wrap:wrap;">
                                <button type="button" class="tool-action-btn" id="wireAddToReport">أضف النتائج إلى التقرير</button>
                            </div>
                        </div>
                    `;

                    const addReportBtn = results.querySelector('#wireAddToReport');
                    if (addReportBtn) {
                        addReportBtn.addEventListener('click', () => {
                            window.energyReport.addCard({
                                type: 'اختيار مقطع السلك',
                                summary: `سلك ${suggestion.recommendedWireSizeMM2} مم² لمسار ${formatNumber(values.distance, 1)} م عند ${formatNumber(values.current, 1)} أمبير`,
                                values: {
                                    'مقطع السلك المقترح': `${suggestion.recommendedWireSizeMM2} مم²`,
                                    'هبوط الجهد': `${formatNumber(suggestion.voltageDrop, 2)} فولت`,
                                    'فقد القدرة': `${formatNumber(suggestion.powerLoss, 2)} وات`,
                                }
                            });
                        });
                    }
                }
            });
        }


        function renderToolPricing(container) {
            if (!container) {
                return;
            }

            const pricingData = {
                panels: [
                    { model: 'Trina Solar 550W', price: '90 - 105 دينار', unit: 'لللوح' },
                    { model: 'Jinko Solar 545W', price: '88 - 102 دينار', unit: 'لللوح' },
                    { model: 'LONGi Solar 540W', price: '85 - 100 دينار', unit: 'لللوح' },
                ],
                inverters: [
                    { model: 'Huawei 5KTL', price: '600 - 700 دينار', unit: 'للقطعة' },
                    { model: 'SMA Sunny Boy 5.0', price: '750 - 850 دينار', unit: 'للقطعة' },
                    { model: 'Solis 10K ثلاثي', price: '900 - 1050 دينار', unit: 'للقطعة' },
                ],
                batteries: [
                    { model: 'Pylontech US2000 2.4kWh', price: '450 - 550 دينار', unit: 'للبطارية' },
                    { model: 'LG Chem RESU 10H', price: '2800 - 3200 دينار', unit: 'للبطارية' },
                    { model: 'Narada 12V 200Ah', price: '200 - 240 دينار', unit: 'للبطارية' },
                ],
                structures: [
                    { model: 'هياكل تثبيت ألمنيوم للأسطح', price: '15 - 25 دينار', unit: 'لكل كيلوواط' },
                    { model: 'هياكل تثبيت حديد مجلفن أرضية', price: '10 - 18 دينار', unit: 'لكل كيلوواط' },
                ],
            };

            const renderTable = (title, items) => `
                <div>
                    <h3 class="text-xl" style="margin-bottom:12px;">${title}</h3>
                    <table class="tool-table">
                        <thead>
                            <tr><th>الموديل/النوع</th><th>نطاق السعر</th><th>الوحدة</th></tr>
                        </thead>
                        <tbody>
                            ${items.map(item => `<tr><td>${item.model}</td><td>${item.price}</td><td>${item.unit}</td></tr>`).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            container.innerHTML = `
                <div class="tool-section">
                    <h3>مرجع أسعار المكونات (دينار أردني)</h3>
                    <p>القيم التالية تعكس متوسط الأسعار في السوق المحلي. يُنصح دائماً بطلب عروض رسمية من الموردين قبل إعداد عرض السعر النهائي.</p>
                    <div class="tool-pricing-grid" style="display:grid; gap:24px;">
                        ${renderTable('الألواح الشمسية', pricingData.panels)}
                        ${renderTable('العواكس (Inverters)', pricingData.inverters)}
                        ${renderTable('البطاريات', pricingData.batteries)}
                        ${renderTable('هياكل التركيب والإكسسوارات', pricingData.structures)}
                    </div>
                    <div class="tool-notice" style="margin-top:18px;">
                        <span class="tool-note-icon">📊</span>
                        <div>هذه الأسعار لا تشمل الضريبة أو تكاليف النقل والتركيب. حدّث القيم بناءً على فئة المشروع والموردين المعتمدين لديك.</div>
                    </div>
                </div>
            `;
        }

        function renderToolReport(container) {
            if (!container) {
                return;
            }

            container.innerHTML = `
                <div class="tool-section">
                    <h3>التقرير الشامل</h3>
                    <p>اجمع نتائج الأدوات المختلفة في تقرير واحد. أضف النتائج من الحاسبات، ثم انسخ التقرير أو امسحه لإعادة البدء.</p>
                    <div class="tool-report-actions" style="display:flex; gap:12px; flex-wrap:wrap; margin-top:16px;">
                        <button type="button" class="tool-action-btn" id="exportReportButton">نسخ التقرير إلى الحافظة</button>
                        <button type="button" class="tool-action-btn secondary" id="clearReportButton">مسح جميع البطاقات</button>
                    </div>
                </div>
                <div class="tool-results-card" id="reportCardsContainer"></div>
            `;

            const cardsContainer = container.querySelector('#reportCardsContainer');
            const copyButton = container.querySelector('#exportReportButton');
            const clearButton = container.querySelector('#clearReportButton');

            const orientationText = {
                portrait: 'وضع طولي (Portrait)',
                landscape: 'وضع عرضي (Landscape)'
            };

            const escapeHtml = (value) => String(value).replace(/[&<>"]/g, (char) => ({
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;'
            })[char] || char);

            function buildReportString(cards) {
                if (!cards.length) {
                    return '';
                }
                const title = 'ملخص تقرير نظام الطاقة الشمسية';
                const separator = '='.repeat(title.length);
                const sections = cards.map(card => {
                    const values = Object.entries(card.values || {})
                        .map(([key, value]) => `- ${key}: ${value}`)
                        .join('\n');
                    return `## ${card.type} ##
${card.summary || ''}
${values}`;
                });
                return `${title}
${separator}

${sections.join('\n\n' + '-'.repeat(24) + '\n\n')}`;
        }

            function renderCards() {
                const cards = window.energyReport.getCards();
                if (!cards.length) {
                    cardsContainer.innerHTML = `
                        <div class="tool-section" style="text-align:center;">
                            <h4>لا توجد بطاقات مضافة بعد</h4>
                            <p>استخدم زر "أضف إلى التقرير" في الأدوات المختلفة لعرض نتائجك هنا.</p>
                        </div>
                    `;
                    return;
                }

                const cardsHtml = cards.map(card => {
                    const valuesHtml = Object.entries(card.values || {}).map(([key, value]) => {
                        let displayValue = value;
                        if (orientationText[value]) {
                            displayValue = orientationText[value];
                        }
                        return `
                            <div class="tool-stat">
                                <span>${escapeHtml(key)}</span>
                                <strong>${escapeHtml(displayValue)}</strong>
                            </div>
                        `;
                    }).join('');

                    return `
                        <div class="tool-section report-card" data-report-card="${escapeHtml(card.id)}">
                            <div class="tool-section-header" style="display:flex; justify-content:space-between; align-items:flex-start; gap:12px;">
                                <div>
                                    <h4 style="margin:0 0 8px;">${escapeHtml(card.type || 'بطاقة تقرير')}</h4>
                                    <p style="margin:0; color:var(--text-secondary); font-size:13px;">${escapeHtml(card.summary || '')}</p>
                                </div>
                                <button type="button" class="tool-action-btn light" data-remove-report="${escapeHtml(card.id)}">إزالة</button>
                            </div>
                            <div class="tool-results-grid cols-2" style="margin-top:16px;">${valuesHtml}</div>
                        </div>
                    `;
                }).join('');

                cardsContainer.innerHTML = cardsHtml;
            }

            renderCards();

            const reportUpdateHandler = () => renderCards();
            document.addEventListener('energy-report-update', reportUpdateHandler);

            cardsContainer.addEventListener('click', (event) => {
                const target = event.target.closest('[data-remove-report]');
                if (!target) {
                    return;
                }
                const cardId = target.getAttribute('data-remove-report');
                window.energyReport.removeCard(cardId);
            });

            copyButton?.addEventListener('click', async () => {
                const cards = window.energyReport.getCards();
                if (!cards.length) {
                    alert('لا توجد بيانات لنسخها. أضف بعض البطاقات أولاً.');
                    return;
                }
                const reportString = buildReportString(cards);
                try {
                    if (navigator.clipboard?.writeText) {
                        await navigator.clipboard.writeText(reportString);
                    } else {
                        const textarea = document.createElement('textarea');
                        textarea.value = reportString;
                        textarea.style.position = 'fixed';
                        textarea.style.opacity = '0';
                        document.body.appendChild(textarea);
                        textarea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textarea);
                    }
                    copyButton.classList.add('success');
                    copyButton.textContent = 'تم النسخ ✓';
                    setTimeout(() => {
                        copyButton.classList.remove('success');
                        copyButton.textContent = 'نسخ التقرير إلى الحافظة';
                    }, 2000);
                } catch (error) {
                    console.error('Failed to copy report', error);
                    alert('تعذر نسخ التقرير. يرجى المحاولة مرة أخرى.');
                }
            });

            clearButton?.addEventListener('click', () => {
                if (!window.energyReport.getCards().length) {
                    return;
                }
                if (confirm('سيتم مسح جميع بطاقات التقرير. هل أنت متأكد؟')) {
                    window.energyReport.clear();
                }
            });

            return () => {
                document.removeEventListener('energy-report-update', reportUpdateHandler);
            };
        }

        // Energy Optimization Modal Functions
        function openEnergyOptimization() {
            const modal = document.getElementById('energyOptimizationModal');
            if (modal) {
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';

                // Ensure welcome content is visible when modal opens
                setTimeout(() => {
                    const welcomeContent = document.getElementById('welcomeContent');
                    const welcomeTitle = welcomeContent?.querySelector('.modal-welcome-title');

                    if (welcomeContent) {
                        welcomeContent.style.display = 'flex';
                        welcomeContent.style.visibility = 'visible';
                        welcomeContent.style.opacity = '1';
                    }

                    if (welcomeTitle) {
                        welcomeTitle.style.display = 'block';
                        welcomeTitle.style.visibility = 'visible';
                        welcomeTitle.style.opacity = '1';
                    }
                }, 100);
            }
        }

        function closeEnergyOptimization() {
            const modal = document.getElementById('energyOptimizationModal');
            if (modal) {
                modal.classList.remove('active');
                document.body.style.overflow = 'auto';
            }
        }


        // Conversation memory for Energy Optimization modal
        let energyModalConversation = [];

        function startNewChat() {
            const welcomeContent = document.getElementById('welcomeContent');
            const centerInput = document.getElementById('centerInputContainer');
            const bottomInput = document.getElementById('bottomInputContainer');
            const chatContentArea = document.getElementById('chatContentArea');

            setActiveEnergyToolItem('assistant');

            // Reset conversation memory before adjusting UI
            energyModalConversation = [];
            showChatInterface(true);

            // Clear all existing messages from chat content area
            const existingMessages = chatContentArea.querySelectorAll('.message');
            existingMessages.forEach(message => message.remove());

            // Remove any existing messages container
            const messagesContainer = chatContentArea.querySelector('.messages-list');
            if (messagesContainer) {
                messagesContainer.remove();
            }

            // Remove any messages containers that might be hiding the welcome content
            const allMessagesContainers = chatContentArea.querySelectorAll('.messages-container');
            allMessagesContainers.forEach(container => container.remove());

            // Reset chat content area to initial state
            if (chatContentArea) {
                chatContentArea.style.display = 'flex';
                chatContentArea.style.flexDirection = 'column';
                chatContentArea.style.justifyContent = 'center';
                chatContentArea.style.alignItems = 'center';
                chatContentArea.style.textAlign = 'center';
                chatContentArea.style.padding = '40px 20px';
                chatContentArea.style.position = 'relative';
                chatContentArea.style.height = 'calc(100vh - 120px)';
                chatContentArea.style.overflow = 'visible';
                chatContentArea.classList.remove('chat-mode');
            }

            // Recreate welcome content if it was cleared by previous messages
            if (welcomeContent) {
                // Check if welcome content was cleared (no children or only messages-list)
                const hasWelcomeElements = welcomeContent.querySelector('.modal-welcome-title');

                if (!hasWelcomeElements) {
                    // Clear any existing content
                    welcomeContent.innerHTML = '';

                    // Recreate welcome title
                    const welcomeTitle = document.createElement('h2');
                    welcomeTitle.className = 'modal-welcome-title';
                    welcomeTitle.textContent = 'Welcome';
                    welcomeContent.appendChild(welcomeTitle);

                    // Recreate center input if it doesn't exist
                    const centerInputContainer = document.createElement('div');
                    centerInputContainer.className = 'modal-input-container center-input';
                    centerInputContainer.id = 'centerInputContainer';

                    centerInputContainer.innerHTML = `
                        <div class="modal-input-wrapper">
                            <button class="modal-action-btn" title="إرفاق ملف">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.44 11.05L12.25 20.24C11.12 21.37 9.31 21.37 8.18 20.24C7.05 19.11 7.05 17.3 8.18 16.17L15.84 8.51C16.97 7.38 18.78 7.38 19.91 8.51C21.04 9.64 21.04 11.45 19.91 12.58L13.76 18.73C13.2 19.29 12.31 19.29 11.75 18.73C11.19 18.17 11.19 17.28 11.75 16.72L17.9 10.57" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <button class="modal-action-btn" title="تسجيل صوتي">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                    <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                                    <path d="M5 10V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                                    <path d="M12 20V23M8 23H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                </svg>
                            </button>
                            <textarea
                                class="modal-input-field"
                                id="modalMessageInput"
                                placeholder="اسأل عن أي شيء..."
                                rows="1"
                            ></textarea>
                            <button class="modal-send-btn" onclick="sendModalMessage()" title="إرسال">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>
                    `;

                    welcomeContent.appendChild(centerInputContainer);
                }

                // Show welcome content with proper styling
                welcomeContent.style.display = 'flex';
                welcomeContent.style.flexDirection = 'column';
                welcomeContent.style.alignItems = 'center';
                welcomeContent.style.justifyContent = 'center';
                welcomeContent.style.height = '100%';
                welcomeContent.style.position = 'relative';
                welcomeContent.style.visibility = 'visible';
                welcomeContent.style.opacity = '1';
                welcomeContent.style.zIndex = '1';
                welcomeContent.style.textAlign = 'center';
                welcomeContent.style.maxWidth = 'none';
            }

            // Show welcome elements with explicit styling
            const welcomeTitle = welcomeContent?.querySelector('.modal-welcome-title');
            const centerInputFromWelcome = welcomeContent?.querySelector('.center-input');

            if (welcomeTitle) {
                welcomeTitle.style.display = 'block';
                welcomeTitle.style.visibility = 'visible';
                welcomeTitle.style.opacity = '1';
                welcomeTitle.style.fontSize = '36px';
                welcomeTitle.style.fontWeight = '700';
                welcomeTitle.style.marginBottom = '32px';
                welcomeTitle.style.textAlign = 'center';
                welcomeTitle.style.color = '#ffffff';
                welcomeTitle.style.textShadow = '0 4px 20px rgba(255, 255, 255, 0.4)';
            }

            // Show center input and hide bottom input
            if (centerInputFromWelcome) {
                centerInputFromWelcome.style.display = 'flex';
                centerInputFromWelcome.style.visibility = 'visible';
                centerInputFromWelcome.style.opacity = '1';
            }
            if (centerInput) {
                centerInput.style.display = 'flex';
                centerInput.style.visibility = 'visible';
                centerInput.style.opacity = '1';
            }
            if (bottomInput) {
                bottomInput.style.display = 'none';
            }

            // Clear both inputs
            const modalInput = document.getElementById('modalMessageInput');
            const modalInputBottom = document.getElementById('modalMessageInputBottom');
            if (modalInput) modalInput.value = '';
            if (modalInputBottom) modalInputBottom.value = '';
        }



        async function sendModalMessage() {
            const messageInput = document.getElementById('modalMessageInput');
            if (!messageInput) return;

            const message = messageInput.value.trim();
            if (!message) return;

            switchToChatMode();
            addModalMessage(message, 'user');
            try { scrollToBottomSafe(); updateBottomPadding(); } catch (e) {}
            messageInput.value = '';
            try {
                const aiText = await requestAIResponse(message);
                addModalMessage(aiText, 'ai');
                updateBottomPadding();
            } catch (e) {
                addModalMessage('Sorry, I could not reach the AI service. Please try again.', 'ai');
            }
        }

        async function sendModalMessageFromBottom() {
            const messageInput = document.getElementById('modalMessageInputBottom');
            if (!messageInput) return;

            const message = messageInput.value.trim();
            if (!message) return;

            addModalMessage(message, 'user');
            try { scrollToBottomSafe(); updateBottomPadding(); } catch (e) {}
            messageInput.value = '';
            messageInput.style.height = 'auto';
            try {
                const aiText = await requestAIResponse(message);
                addModalMessage(aiText, 'ai');
                updateBottomPadding();
            } catch (e) {
                addModalMessage('Sorry, I could not reach the AI service. Please try again.', 'ai');
            }
        }

        function updateBottomPadding() {
            const input = document.getElementById('bottomInputContainer');
            const scroller = document.querySelector('.modal-chat-area');
            const lists = document.querySelectorAll('.messages-list, .chat-content-area');
            const h = input ? input.getBoundingClientRect().height : 0;
            const pad = Math.min(window.innerHeight * 0.6, Math.max(160, Math.round(h + 56)));
            if (scroller) scroller.style.paddingBottom = pad + 'px';
            lists.forEach(el => el.style.paddingBottom = pad + 'px');
            ensureInputSpacer();
        }

        function ensureInputSpacer() {
            try {
                const list = document.querySelector('.messages-list');
                const input = document.getElementById('bottomInputContainer');
                if (!list || !input) return;
                let spacer = list.querySelector('#inputSpacer');
                const h = input.getBoundingClientRect().height || 0;
                const need = Math.round(h + 48);
                if (!spacer) {
                    spacer = document.createElement('div');
                    spacer.id = 'inputSpacer';
                    spacer.style.width = '100%';
                    spacer.style.height = need + 'px';
                    spacer.style.pointerEvents = 'none';
                    list.appendChild(spacer);
                } else {
                    spacer.style.height = need + 'px';
                    // Always ensure spacer stays as the last element
                    // so it doesn't appear between consecutive messages
                    list.appendChild(spacer);
                }
            } catch (e) {}
        }

        function switchToChatMode() {
            const welcomeContent = document.getElementById('welcomeContent');
            const centerInput = document.getElementById('centerInputContainer');
            const bottomInput = document.getElementById('bottomInputContainer');
            const chatContentArea = document.getElementById('chatContentArea');
            const chatAreaContainer = document.querySelector('#energyOptimizationModal .modal-chat-area');

            setActiveEnergyToolItem('assistant');
            hideToolInterface();

            if (chatAreaContainer) {
                chatAreaContainer.style.display = 'flex';
            }

            // Hide center input if it exists
            if (centerInput) {
                centerInput.style.display = 'none';
            }

            // Change welcome content styling for messages
            welcomeContent.style.textAlign = 'left';
            welcomeContent.style.maxWidth = '100%';
            welcomeContent.style.display = 'flex';
            welcomeContent.style.flexDirection = 'column';
            welcomeContent.style.alignItems = 'center';
            welcomeContent.style.justifyContent = 'flex-start';
            welcomeContent.style.height = 'auto';
            welcomeContent.style.paddingBottom = '0px';

            // Change chat area styling for messages
            chatContentArea.style.justifyContent = 'flex-start';
            chatContentArea.style.textAlign = 'left';
            chatContentArea.style.alignItems = 'stretch';
            chatContentArea.style.padding = '20px';

            // Show bottom input
            bottomInput.style.display = 'flex';

            // Ensure enough bottom space so last message stays above the input bar (dynamic)
            updateBottomPadding();
        }

        // Helper to scroll to bottom while keeping last message above the fixed input bar
        function scrollToBottomSafe() {
            const scroller = document.querySelector('.modal-chat-area');
            if (!scroller) return;
            const input = document.getElementById('bottomInputContainer');
            const inputH = input ? input.getBoundingClientRect().height : 160;
            const extra = 24; // small breathing space
            const target = Math.max(0, scroller.scrollHeight - scroller.clientHeight - inputH - extra);
            scroller.scrollTo({ top: target, behavior: 'smooth' });
        }

















        function addModalMessage(content, sender) {
            const welcomeContent = document.getElementById('welcomeContent');
            const scroller = document.querySelector('.modal-chat-area');
            const nearBottom = (() => {
                if (!scroller) return true;
                const delta = scroller.scrollHeight - scroller.scrollTop - scroller.clientHeight;
                return delta < 200; // auto-scroll only if user is near bottom
            })();

            // Replace welcome content with messages container on first message
            if (!welcomeContent.querySelector('.messages-list')) {
                // Clear welcome content
                welcomeContent.innerHTML = '';

            // Create messages container
            const messagesContainer = document.createElement('div');
            messagesContainer.className = 'messages-list';
            messagesContainer.style.display = 'flex';
            messagesContainer.style.flexDirection = 'column';
            messagesContainer.style.gap = '0px'; // no space between message and reply
            messagesContainer.style.width = '100%';
            messagesContainer.style.maxWidth = '800px';

                welcomeContent.appendChild(messagesContainer);
            }

            const messagesContainer = welcomeContent.querySelector('.messages-list');

            // Create message
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;



            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            // Basic formatting: escape HTML and preserve line breaks
            const safe = (content || '')
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/\n/g, '<br>');
            messageContent.innerHTML = safe;
            // Keep inline styles consistent with original centered design
            messageContent.style.width = '100%';
            messageContent.style.maxWidth = '700px';
            messageContent.style.padding = '20px 24px';
            messageContent.style.backgroundColor = 'rgba(255, 255, 255, 0.08)';
            messageContent.style.border = '1px solid rgba(255, 255, 255, 0.12)';
            messageContent.style.borderRadius = '20px';
            messageContent.style.color = 'white';
            messageContent.style.lineHeight = '1.7';
            messageContent.style.textAlign = 'center';
            messageContent.style.fontSize = '15px';
            messageContent.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.2)';
            messageContent.style.backdropFilter = 'blur(10px)';
            messageContent.style.margin = '0 auto';

            messageDiv.appendChild(messageContent);

            // Add message to messages container
            messagesContainer.appendChild(messageDiv);

            if (nearBottom && scroller) {
                scrollToBottomSafe();
            }
            updateBottomPadding();
            ensureInputSpacer();
        }

        // Call backend AI for a smarter response
        async function requestAIResponse(userMessage) {
            try {
                // Maintain conversation context
                energyModalConversation.push({ role: 'user', content: userMessage });

                // Build endpoint from configSystem if available
                let chatURL = '/api/chat';
                try {
                    if (window.configSystem) {
                        chatURL = window.configSystem.getAPIEndpoint('chat');
                    }
                } catch (e) {
                    console.warn('Failed to get API endpoint from configSystem:', e);
                }

                const resp = await fetch(chatURL, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ messages: energyModalConversation, temperature: 0.3, max_tokens: 600 })
                });

                if (!resp.ok) {
                    throw new Error(`HTTP ${resp.status}: ${resp.statusText}`);
                }

                const data = await resp.json();
                const ai = data?.choices?.[0]?.message?.content || 'I could not generate a response.';

                // Save assistant reply to conversation
                energyModalConversation.push({ role: 'assistant', content: ai });

                return ai;
            } catch (error) {
                console.error('Error in requestAIResponse:', error);
                throw error; // Re-throw to be handled by calling function
            }
        }

        function generateModalResponse(userMessage) {
            const responses = {
                'energy bill': 'I can help you analyze your energy consumption! Please share your recent electricity bill, and I\'ll provide:\n\n• Peak usage patterns\n• Cost breakdown analysis\n• Optimization opportunities\n• Potential savings calculation\n\nYou can upload an image or PDF of your bill.',

                'solar': 'Great choice! Solar panels can significantly reduce your energy costs. Here\'s what I need to provide personalized recommendations:\n\n• Your location\n• Average monthly electricity bill\n• Roof size and orientation\n• Current energy usage patterns\n\nBased on this, I can calculate potential savings and ROI.',

                'smart home': 'Smart home devices can reduce energy consumption by 10-25%! Here are my top recommendations:\n\n🌡️ Smart Thermostats (15-20% savings)\n💡 Smart LED Bulbs (75% less energy)\n🔌 Smart Plugs (eliminate phantom loads)\n📱 Energy Monitoring Systems\n\nWhich area would you like to start with?',

                'costs': 'Here are proven ways to reduce your electricity costs:\n\n1. **Immediate Actions:**\n   • Switch to LED lighting\n   • Unplug unused devices\n   • Adjust thermostat settings\n\n2. **Medium-term:**\n   • Upgrade to efficient appliances\n   • Install smart thermostats\n   • Improve insulation\n\n3. **Long-term:**\n   • Solar panel installation\n   • Energy storage systems\n\nWould you like detailed guidance on any of these?'
            };

            for (const [key, response] of Object.entries(responses)) {
                if (userMessage.toLowerCase().includes(key)) {
                    return response;
                }
            }

            return 'I\'m here to help with all your energy optimization needs! I can assist with:\n\n• Energy bill analysis\n• Solar panel recommendations\n• Smart home solutions\n• Cost reduction strategies\n• Efficiency improvements\n\nWhat specific area would you like to explore?';
        }

        // Auto-resize textarea for modal
        document.addEventListener('DOMContentLoaded', function() {
            const modalMessageInput = document.getElementById('modalMessageInput');
            if (modalMessageInput) {
                modalMessageInput.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = Math.min(this.scrollHeight, 120) + 'px';
                    try { updateBottomPadding(); } catch (e) {}
                });

                modalMessageInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendModalMessage();
                    }
                });

                // Bottom input event listeners
                const modalMessageInputBottom = document.getElementById('modalMessageInputBottom');
                if (modalMessageInputBottom) {
                    modalMessageInputBottom.addEventListener('input', function() {
                        this.style.height = 'auto';
                        this.style.height = Math.min(this.scrollHeight, 120) + 'px';
                        try { updateBottomPadding(); } catch (e) {}
                    });

                    modalMessageInputBottom.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            sendModalMessageFromBottom();
                        }
                    });
                }
            }
            try { updateBottomPadding(); } catch (e) {}
            window.addEventListener('resize', function(){ try { updateBottomPadding(); } catch (e) {} });
        });

        // Removed PM modal auto-resize (standalone page)
    </script>



    <!-- Auth system will be loaded by auth-system.js -->

    <!-- Glassmorphism System - Priority Load -->
    <script src="js/glassmorphism.js"></script>

    <!-- Core Systems - Load first -->
    <script src="js/config.js"></script>
    <script src="js/state-management.js"></script>
    <script src="js/security-system.js"></script>
    <script src="js/performance-optimizations.js"></script>
    <script src="js/notification-system.js"></script>
    <script src="js/analytics-system.js"></script>

    <!-- Core JavaScript -->
    <script src="js/jarvis-config.js"></script>
    <!-- Language system removed - English only -->
    <script src="js/auth-system.js"></script>
    <script src="js/embedded-map.js"></script>

    <script src="js/main.js"></script>

    <!-- Energy Security Modal Scripts (independent from Optimization) -->
    <script>
        function openEnergySecurity() {
            const modal = document.getElementById('energySecurityModal');
            if (!modal) return;
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
            // Normalize welcome title visibility/size immediately
            const wc = document.getElementById('welcomeContentSec');
            const wt = wc ? wc.querySelector('.modal-welcome-title') : null;
            if (wc) { wc.style.display='flex'; wc.style.visibility='visible'; wc.style.opacity='1'; }
            if (wt) { wt.style.display='block'; wt.style.visibility='visible'; wt.style.opacity='1'; wt.style.fontSize='36px'; }
        }

        function closeEnergySecurity() {
            const modal = document.getElementById('energySecurityModal');
            if (!modal) return;
            modal.classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        let energySecurityConversation = [];

        function startNewChatSecurity() {
            const welcomeContent = document.getElementById('welcomeContentSec');
            const centerInput = document.getElementById('centerInputContainerSec');
            const bottomInput = document.getElementById('bottomInputContainerSec');
            const chatContentArea = document.getElementById('chatContentAreaSec');

            // Clear existing messages
            const existingMessages = chatContentArea.querySelectorAll('.message');
            existingMessages.forEach(el => el.remove());
            const list = chatContentArea.querySelector('.messages-list');
            if (list) list.remove();

            // Reset styles
            if (chatContentArea) {
                chatContentArea.style.display = 'flex';
                chatContentArea.style.flexDirection = 'column';
                chatContentArea.style.justifyContent = 'center';
                chatContentArea.style.alignItems = 'center';
                chatContentArea.style.textAlign = 'center';
                chatContentArea.style.padding = '40px 20px';
                chatContentArea.style.position = 'relative';
                chatContentArea.style.height = 'calc(100vh - 120px)';
                chatContentArea.style.overflow = 'visible';
                chatContentArea.classList.remove('chat-mode');
            }

            energySecurityConversation = [];

            // Show center input
            if (centerInput) centerInput.style.display = 'flex';
            if (bottomInput) bottomInput.style.display = 'none';

            const top = document.getElementById('modalMessageInputSec');
            const bottom = document.getElementById('modalMessageInputBottomSec');
            if (top) top.value = '';
            if (bottom) bottom.value = '';
        }

        function switchToChatModeSec() {
            const welcomeContent = document.getElementById('welcomeContentSec');
            const centerInput = document.getElementById('centerInputContainerSec');
            const bottomInput = document.getElementById('bottomInputContainerSec');
            const chatContentArea = document.getElementById('chatContentAreaSec');
            if (centerInput) centerInput.style.display = 'none';
            if (welcomeContent) {
                welcomeContent.style.textAlign = 'left';
                welcomeContent.style.maxWidth = '100%';
                welcomeContent.style.display = 'flex';
                welcomeContent.style.flexDirection = 'column';
                welcomeContent.style.alignItems = 'center';
                welcomeContent.style.justifyContent = 'flex-start';
                welcomeContent.style.height = 'auto';
                welcomeContent.style.paddingBottom = '0px';
            }
            if (chatContentArea) {
                chatContentArea.style.justifyContent = 'flex-start';
                chatContentArea.style.textAlign = 'left';
                chatContentArea.style.alignItems = 'stretch';
                chatContentArea.style.padding = '20px';
            }
            if (bottomInput) bottomInput.style.display = 'flex';
            updateBottomPaddingSec();
        }

        function scrollToBottomSafeSec() {
            const scroller = document.querySelector('#energySecurityModal .modal-chat-area');
            if (!scroller) return;
            const input = document.getElementById('bottomInputContainerSec');
            const inputH = input ? input.getBoundingClientRect().height : 160;
            const extra = 24;
            const target = Math.max(0, scroller.scrollHeight - scroller.clientHeight - inputH - extra);
            scroller.scrollTo({ top: target, behavior: 'smooth' });
        }

        function addModalMessageSec(content, sender) {
            const welcomeContent = document.getElementById('welcomeContentSec');
            const scroller = document.querySelector('#energySecurityModal .modal-chat-area');
            const nearBottom = (() => {
                if (!scroller) return true;
                const delta = scroller.scrollHeight - scroller.scrollTop - scroller.clientHeight;
                return delta < 200;
            })();
            if (!welcomeContent.querySelector('.messages-list')) {
                welcomeContent.innerHTML = '';
                const messagesContainer = document.createElement('div');
                messagesContainer.className = 'messages-list';
                messagesContainer.style.display = 'flex';
                messagesContainer.style.flexDirection = 'column';
                messagesContainer.style.gap = '0px';
                messagesContainer.style.width = '100%';
                messagesContainer.style.maxWidth = '800px';
                welcomeContent.appendChild(messagesContainer);
            }
            const messagesContainer = welcomeContent.querySelector('.messages-list');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            const safe = (content || '')
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/\n/g, '<br>');
            messageContent.innerHTML = safe;
            messageContent.style.width = '100%';
            messageContent.style.maxWidth = '700px';
            messageContent.style.padding = '20px 24px';
            messageContent.style.backgroundColor = 'rgba(255, 255, 255, 0.08)';
            messageContent.style.border = '1px solid rgba(255, 255, 255, 0.12)';
            messageContent.style.borderRadius = '20px';
            messageContent.style.color = 'white';
            messageContent.style.lineHeight = '1.7';
            messageContent.style.textAlign = 'center';
            messageContent.style.fontSize = '15px';
            messageContent.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.2)';
            messageContent.style.backdropFilter = 'blur(10px)';
            messageContent.style.margin = '0 auto';
            messageDiv.appendChild(messageContent);
            messagesContainer.appendChild(messageDiv);
            if (nearBottom && scroller) scrollToBottomSafeSec();
            updateBottomPaddingSec();
            ensureInputSpacerSec();
        }

        async function requestAIResponseSec(userMessage) {
            try {
                energySecurityConversation.push({ role: 'user', content: userMessage });
                let chatURL = '/api/chat';
                try {
                    if (window.configSystem) chatURL = window.configSystem.getAPIEndpoint('chat');
                } catch (e) {
                    console.warn('Failed to get API endpoint from configSystem:', e);
                }

                const resp = await fetch(chatURL, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ messages: energySecurityConversation, temperature: 0.3, max_tokens: 600 })
                });

                if (!resp.ok) throw new Error(`HTTP ${resp.status}: ${resp.statusText}`);

                const data = await resp.json();
                const ai = data?.choices?.[0]?.message?.content || 'I could not generate a response.';
                energySecurityConversation.push({ role: 'assistant', content: ai });
                return ai;
            } catch (error) {
                console.error('Error in requestAIResponseSec:', error);
                throw error; // Re-throw to be handled by calling function
            }
        }

        async function sendModalMessageSec() {
            const messageInput = document.getElementById('modalMessageInputSec');
            if (!messageInput) return;
            const message = messageInput.value.trim();
            if (!message) return;
            switchToChatModeSec();
            addModalMessageSec(message, 'user');
            try { scrollToBottomSafeSec(); updateBottomPaddingSec(); } catch (e) {}
            messageInput.value = '';
            try { const ai = await requestAIResponseSec(message); addModalMessageSec(ai, 'ai'); updateBottomPaddingSec(); }
            catch (e) { addModalMessageSec('Sorry, I could not reach the AI service. Please try again.', 'ai'); }
        }

        async function sendModalMessageFromBottomSec() {
            const messageInput = document.getElementById('modalMessageInputBottomSec');
            if (!messageInput) return;
            const message = messageInput.value.trim();
            if (!message) return;
            addModalMessageSec(message, 'user');
            try { scrollToBottomSafeSec(); updateBottomPaddingSec(); } catch (e) {}
            messageInput.value = '';
            messageInput.style.height = 'auto';
            try { const ai = await requestAIResponseSec(message); addModalMessageSec(ai, 'ai'); updateBottomPaddingSec(); }
            catch (e) { addModalMessageSec('Sorry, I could not reach the AI service. Please try again.', 'ai'); }
        }

        function updateBottomPaddingSec() {
            const input = document.getElementById('bottomInputContainerSec');
            const scroller = document.querySelector('#energySecurityModal .modal-chat-area');
            const lists = document.querySelectorAll('#energySecurityModal .messages-list, #energySecurityModal .chat-content-area');
            const h = input ? input.getBoundingClientRect().height : 0;
            const pad = Math.min(window.innerHeight * 0.6, Math.max(160, Math.round(h + 56)));
            if (scroller) scroller.style.paddingBottom = pad + 'px';
            lists.forEach(el => el.style.paddingBottom = pad + 'px');
            ensureInputSpacerSec();
        }

        function ensureInputSpacerSec() {
            try {
                const list = document.querySelector('#energySecurityModal .messages-list');
                const input = document.getElementById('bottomInputContainerSec');
                if (!list || !input) return;
                let spacer = list.querySelector('#inputSpacerSec');
                const h = input.getBoundingClientRect().height || 0;
                const need = Math.round(h + 48);
                if (!spacer) {
                    spacer = document.createElement('div');
                    spacer.id = 'inputSpacerSec';
                    spacer.style.width = '100%';
                    spacer.style.height = need + 'px';
                    spacer.style.pointerEvents = 'none';
                    list.appendChild(spacer);
                } else {
                    spacer.style.height = need + 'px';
                    list.appendChild(spacer);
                }
            } catch (e) {}
        }

        document.addEventListener('DOMContentLoaded', function() {
            const inputTop = document.getElementById('modalMessageInputSec');
            if (inputTop) {
                inputTop.addEventListener('input', function(){ this.style.height='auto'; this.style.height=Math.min(this.scrollHeight,120)+'px'; try{updateBottomPaddingSec();}catch(e){} });
                inputTop.addEventListener('keydown', function(e){ if(e.key==='Enter' && !e.shiftKey){ e.preventDefault(); sendModalMessageSec(); } });
            }
            const inputBottom = document.getElementById('modalMessageInputBottomSec');
            if (inputBottom) {
                inputBottom.addEventListener('input', function(){ this.style.height='auto'; this.style.height=Math.min(this.scrollHeight,120)+'px'; try{updateBottomPaddingSec();}catch(e){} });
                inputBottom.addEventListener('keydown', function(e){ if(e.key==='Enter' && !e.shiftKey){ e.preventDefault(); sendModalMessageFromBottomSec(); } });
            }
            try { updateBottomPaddingSec(); } catch (e) {}
            window.addEventListener('resize', function(){ try { updateBottomPaddingSec(); } catch(e){} });
        });
    </script>

    <!-- Cloud Energy Modal Scripts (independent) -->
    <script>
        function openCloudEnergy() {
            const modal = document.getElementById('cloudEnergyModal');
            if (!modal) return;
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closeCloudEnergy() {
            const modal = document.getElementById('cloudEnergyModal');
            if (!modal) return;
            modal.classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        let cloudConversation = [];

        function switchToChatModeCloud() {
            const welcomeContent = document.getElementById('welcomeContentCloud');
            const centerInput = document.getElementById('centerInputContainerCloud');
            const bottomInput = document.getElementById('bottomInputContainerCloud');
            const chatContentArea = document.getElementById('chatContentAreaCloud');
            if (centerInput) centerInput.style.display = 'none';
            if (welcomeContent) {
                welcomeContent.style.textAlign = 'left';
                welcomeContent.style.maxWidth = '100%';
                welcomeContent.style.display = 'flex';
                welcomeContent.style.flexDirection = 'column';
                welcomeContent.style.alignItems = 'center';
                welcomeContent.style.justifyContent = 'flex-start';
                welcomeContent.style.height = 'auto';
                welcomeContent.style.paddingBottom = '0px';
            }
            if (chatContentArea) {
                chatContentArea.style.justifyContent = 'flex-start';
                chatContentArea.style.textAlign = 'left';
                chatContentArea.style.alignItems = 'stretch';
                chatContentArea.style.padding = '20px';
            }
            if (bottomInput) bottomInput.style.display = 'flex';
            updateBottomPaddingCloud();
        }

        function scrollToBottomSafeCloud() {
            const scroller = document.querySelector('#cloudEnergyModal .modal-chat-area');
            if (!scroller) return;
            const input = document.getElementById('bottomInputContainerCloud');
            const inputH = input ? input.getBoundingClientRect().height : 160;
            const extra = 24;
            const target = Math.max(0, scroller.scrollHeight - scroller.clientHeight - inputH - extra);
            scroller.scrollTo({ top: target, behavior: 'smooth' });
        }

        function addModalMessageCloud(content, sender) {
            const welcomeContent = document.getElementById('welcomeContentCloud');
            const scroller = document.querySelector('#cloudEnergyModal .modal-chat-area');
            const nearBottom = (() => {
                if (!scroller) return true;
                const delta = scroller.scrollHeight - scroller.scrollTop - scroller.clientHeight;
                return delta < 200;
            })();
            if (!welcomeContent.querySelector('.messages-list')) {
                welcomeContent.innerHTML = '';
                const messagesContainer = document.createElement('div');
                messagesContainer.className = 'messages-list';
                messagesContainer.style.display = 'flex';
                messagesContainer.style.flexDirection = 'column';
                messagesContainer.style.gap = '0px';
                messagesContainer.style.width = '100%';
                messagesContainer.style.maxWidth = '800px';
                welcomeContent.appendChild(messagesContainer);
            }
            const messagesContainer = welcomeContent.querySelector('.messages-list');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            const safe = (content || '')
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/\n/g, '<br>');
            messageContent.innerHTML = safe;
            messageContent.style.width = '100%';
            messageContent.style.maxWidth = '700px';
            messageContent.style.padding = '20px 24px';
            messageContent.style.backgroundColor = 'rgba(255, 255, 255, 0.08)';
            messageContent.style.border = '1px solid rgba(255, 255, 255, 0.12)';
            messageContent.style.borderRadius = '20px';
            messageContent.style.color = 'white';
            messageContent.style.lineHeight = '1.7';
            messageContent.style.textAlign = 'center';
            messageContent.style.fontSize = '15px';
            messageContent.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.2)';
            messageContent.style.backdropFilter = 'blur(10px)';
            messageContent.style.margin = '0 auto';
            messageDiv.appendChild(messageContent);
            messagesContainer.appendChild(messageDiv);
            if (nearBottom && scroller) scrollToBottomSafeCloud();
            updateBottomPaddingCloud();
            ensureInputSpacerCloud();
        }

        async function requestAIResponseCloud(userMessage) {
            try {
                cloudConversation.push({ role: 'user', content: userMessage });
                let chatURL = '/api/chat';
                try {
                    if (window.configSystem) chatURL = window.configSystem.getAPIEndpoint('chat');
                } catch (e) {
                    console.warn('Failed to get API endpoint from configSystem:', e);
                }

                const resp = await fetch(chatURL, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ messages: cloudConversation, temperature: 0.3, max_tokens: 600 })
                });

                if (!resp.ok) throw new Error(`HTTP ${resp.status}: ${resp.statusText}`);

                const data = await resp.json();
                const ai = data?.choices?.[0]?.message?.content || 'I could not generate a response.';
                cloudConversation.push({ role: 'assistant', content: ai });
                return ai;
            } catch (error) {
                console.error('Error in requestAIResponseCloud:', error);
                throw error; // Re-throw to be handled by calling function
            }
        }

        async function sendModalMessageCloud() {
            const messageInput = document.getElementById('modalMessageInputCloud');
            if (!messageInput) return;
            const message = messageInput.value.trim();
            if (!message) return;
            switchToChatModeCloud();
            addModalMessageCloud(message, 'user');
            try { scrollToBottomSafeCloud(); updateBottomPaddingCloud(); } catch (e) {}
            messageInput.value = '';
            try { const ai = await requestAIResponseCloud(message); addModalMessageCloud(ai, 'ai'); updateBottomPaddingCloud(); }
            catch (e) { addModalMessageCloud('Sorry, I could not reach the AI service. Please try again.', 'ai'); }
        }

        async function sendModalMessageFromBottomCloud() {
            const messageInput = document.getElementById('modalMessageInputBottomCloud');
            if (!messageInput) return;
            const message = messageInput.value.trim();
            if (!message) return;
            addModalMessageCloud(message, 'user');
            try { scrollToBottomSafeCloud(); updateBottomPaddingCloud(); } catch (e) {}
            messageInput.value = '';
            messageInput.style.height = 'auto';
            try { const ai = await requestAIResponseCloud(message); addModalMessageCloud(ai, 'ai'); updateBottomPaddingCloud(); }
            catch (e) { addModalMessageCloud('Sorry, I could not reach the AI service. Please try again.', 'ai'); }
        }

        function updateBottomPaddingCloud() {
            const input = document.getElementById('bottomInputContainerCloud');
            const scroller = document.querySelector('#cloudEnergyModal .modal-chat-area');
            const lists = document.querySelectorAll('#cloudEnergyModal .messages-list, #cloudEnergyModal .chat-content-area');
            const h = input ? input.getBoundingClientRect().height : 0;
            const pad = Math.min(window.innerHeight * 0.6, Math.max(160, Math.round(h + 56)));
            if (scroller) scroller.style.paddingBottom = pad + 'px';
            lists.forEach(el => el.style.paddingBottom = pad + 'px');
            ensureInputSpacerCloud();
        }

        function ensureInputSpacerCloud() {
            try {
                const list = document.querySelector('#cloudEnergyModal .messages-list');
                const input = document.getElementById('bottomInputContainerCloud');
                if (!list || !input) return;
                let spacer = list.querySelector('#inputSpacerCloud');
                const h = input.getBoundingClientRect().height || 0;
                const need = Math.round(h + 48);
                if (!spacer) {
                    spacer = document.createElement('div');
                    spacer.id = 'inputSpacerCloud';
                    spacer.style.width = '100%';
                    spacer.style.height = need + 'px';
                    spacer.style.pointerEvents = 'none';
                    list.appendChild(spacer);
                } else {
                    spacer.style.height = need + 'px';
                    list.appendChild(spacer);
                }
            } catch (e) {}
        }

        function startNewChatCloud() {
            const welcomeContent = document.getElementById('welcomeContentCloud');
            const centerInput = document.getElementById('centerInputContainerCloud');
            const bottomInput = document.getElementById('bottomInputContainerCloud');
            const chatContentArea = document.getElementById('chatContentAreaCloud');
            const existing = chatContentArea.querySelectorAll('.message');
            existing.forEach(n => n.remove());
            const list = chatContentArea.querySelector('.messages-list');
            if (list) list.remove();
            if (chatContentArea) {
                chatContentArea.style.display = 'flex';
                chatContentArea.style.flexDirection = 'column';
                chatContentArea.style.justifyContent = 'center';
                chatContentArea.style.alignItems = 'center';
                chatContentArea.style.textAlign = 'center';
                chatContentArea.style.padding = '40px 20px';
                chatContentArea.style.position = 'relative';
                chatContentArea.style.height = 'calc(100vh - 120px)';
                chatContentArea.style.overflow = 'visible';
                chatContentArea.classList.remove('chat-mode');
            }
            cloudConversation = [];
            if (centerInput) centerInput.style.display = 'flex';
            if (bottomInput) bottomInput.style.display = 'none';
            const top = document.getElementById('modalMessageInputCloud');
            const bottom = document.getElementById('modalMessageInputBottomCloud');
            if (top) top.value = '';
            if (bottom) bottom.value = '';
        }

        document.addEventListener('DOMContentLoaded', function() {
            const top = document.getElementById('modalMessageInputCloud');
            if (top) {
                top.addEventListener('input', function(){ this.style.height='auto'; this.style.height=Math.min(this.scrollHeight,120)+'px'; try{updateBottomPaddingCloud();}catch(e){} });
                top.addEventListener('keydown', function(e){ if(e.key==='Enter' && !e.shiftKey){ e.preventDefault(); sendModalMessageCloud(); } });
            }
            const bottom = document.getElementById('modalMessageInputBottomCloud');
            if (bottom) {
                bottom.addEventListener('input', function(){ this.style.height='auto'; this.style.height=Math.min(this.scrollHeight,120)+'px'; try{updateBottomPaddingCloud();}catch(e){} });
                bottom.addEventListener('keydown', function(e){ if(e.key==='Enter' && !e.shiftKey){ e.preventDefault(); sendModalMessageFromBottomCloud(); } });
            }
            try { updateBottomPaddingCloud(); } catch (e) {}
            window.addEventListener('resize', function(){ try { updateBottomPaddingCloud(); } catch(e){} });
        });
    </script>

    <!-- Three.js and Neon Cursor -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.139.2/build/three.module.js",
            "threejs-toys": "https://unpkg.com/threejs-toys@0.0.8/build/threejs-toys.module.cdn.min.js"
        }
    }
    </script>
    <script type="module" src="js/neon-cursor.js"></script>

    <!-- Sidebar hover functionality -->
    <script>
        // Sidebar hover functionality for all modals independently
        function initializeSidebar() {
            const containers = document.querySelectorAll('.energy-optimization-modal, .energy-security-modal, .cloud-energy-modal');
            containers.forEach(container => {
                const sidebar = container.querySelector('.modal-sidebar');
                const hoverArea = container.querySelector('.sidebar-hover-area');
                let isHovered = false;
                let hoverTimeout;
                if (hoverArea && sidebar) {
                    hoverArea.addEventListener('mouseenter', () => {
                        clearTimeout(hoverTimeout);
                        isHovered = true;
                        sidebar.style.transform = 'translateX(0)';
                        sidebar.style.opacity = '1';
                        sidebar.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.3)';
                    });
                    sidebar.addEventListener('mouseenter', () => {
                        clearTimeout(hoverTimeout);
                        isHovered = true;
                        sidebar.style.transform = 'translateX(0)';
                        sidebar.style.opacity = '1';
                        sidebar.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.3)';
                    });
                    sidebar.addEventListener('mouseleave', () => {
                        isHovered = false;
                        hoverTimeout = setTimeout(() => {
                            if (!isHovered) {
                                const isMobile = window.innerWidth <= 768;
                                const translateX = isMobile ? '-290px' : '-270px';
                                sidebar.style.transform = `translateX(${translateX})`;
                                sidebar.style.opacity = '0';
                                sidebar.style.boxShadow = 'none';
                            }
                        }, 500);
                    });
                    window.addEventListener('resize', () => {
                        if (!isHovered) {
                            const isMobile = window.innerWidth <= 768;
                            const translateX = isMobile ? '-290px' : '-270px';
                            sidebar.style.transform = `translateX(${translateX})`;
                            sidebar.style.opacity = '0';
                        }
                    });
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            initializeSidebar();
        });
    </script>
</body>
</html>









